from machine import Pin, ADC
import time

class GroveLightSensor:
    """
    @desc: Interface for Grove Light Sensor
           Supports reading light level and threshold checks

    @example:
        >>> from BeeBrain import bee
        >>> sensor = GroveLightSensor(port=bee.PORT1)
        >>> if sensor.is_bright():
        ...     print("It's bright!")
        >>> if sensor.is_dark():
        ...     print("It's dark!")
    """

    __MAX_ADC = 4095

    def __init__(self, port = None):
        """
        @desc: Initialize light sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = GroveLightSensor(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__adc = ADC(Pin(port.use_pins(1)))

    @property
    def read_light_level(self):
        """
        @desc: Read raw light level from sensor

        @returns:
            - int: Raw light level value (0-4095)

        @example:
            >>> level = sensor.read_light_level
            >>> print(f"Light level: {level}")
        """
        return self.__adc.read()

    @property
    def read_light_percent(self):
        """
        @desc: Read light level as percentage

        @returns:
            - int: Light level as percentage (0-100)

        @example:
            >>> percent = sensor.read_light_percent
            >>> print(f"Light level: {percent}%")
        """
        raw_value = self.read_light_level
        return (raw_value * 100) // __MAX_ADC

    def is_bright(self, threshold_percent=50):
        """
        @desc: Check if light level is above threshold

        @args:
            - threshold_percent: Optional threshold percentage (0-100)

        @returns:
            - bool: True if light level is above threshold

        @example:
            >>> if sensor.is_bright(70):
            ...     print("It's bright!")
        """
        threshold = (threshold_percent * __MAX_ADC) // 100
        return self.read_light_level > threshold

    def is_dark(self, threshold_percent=50):
        """
        @desc: Check if light level is below threshold

        @args:
            - threshold_percent: Optional threshold percentage (0-100)

        @returns:
            - bool: True if light level is below threshold

        @example:
            >>> if sensor.is_dark(30):
            ...     print("It's dark!")
        """
        threshold = (threshold_percent * __MAX_ADC) // 100
        return self.read_light_level <= threshold
