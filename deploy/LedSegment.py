from micropython import const
from machine import Pin
from time import sleep_us, sleep_ms


class LedSegment:
    """
    @desc: Interface for TM1637-based quad 7-segment LED display modules
           Supports numeric display, text, scrolling, and brightness control

    @requires:
        - micropython
        - machine.Pin
        - time (sleep_us, sleep_ms)

    @example:
        >>> from BeeBrain import bee
        >>> segment = LedSegment(bee.PORT1)
        >>> segment.show_number(42)  # Display number
        >>> segment.show("Hi")      # Display text
        >>> segment.brightness(5)    # Set medium brightness

    @version: 1.2.0
    """

    # TM1637 Commands
    TM1637_CMD1 = const(64)   # 0x40 Data command
    TM1637_CMD2 = const(192)  # 0xC0 Address command
    TM1637_CMD3 = const(128)  # 0x80 Display control command
    TM1637_DSP_ON = const(8)  # 0x08 Display on
    TM1637_DELAY = const(10)  # 10us delay between clk/dio pulses
    TM1637_MSB = const(128)   # MSB is decimal point or colon

    # Segment bit patterns for characters (0-9, a-z, blank, dash, star)
    _SEGMENTS = bytearray(
        b'\x3F\x06\x5B\x4F\x66\x6D\x7D\x07\x7F\x6F\x77\x7C\x39\x5E\x79\x71\x3D\x76\x06\x1E\x76\x38\x55\x54\x3F\x73\x67\x50\x6D\x78\x3E\x1C\x2A\x76\x6E\x5B\x00\x40\x63')

    def __init__(self, port = None, brightness: int = 7) -> None:
        """
        @desc: Initialize 7-segment display with specified port and brightness

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - brightness: Initial brightness level (0-7, default: 7)

        @raises:
            - ValueError: If brightness is out of valid range

        @example:
            >>> from BeeBrain import bee
            >>> segment = LedSegment(port=bee.PORT1)  # Full brightness
            >>> segment = LedSegment(port=bee.PORT2, 3)  # Medium brightness
        """
        if port is None:
            raise ValueError("Invalid port")
        self.clk = Pin(port.use_pins(1))
        self.dio = Pin(port.use_pins(2))

        if not 0 <= brightness <= 7:
            raise ValueError("Brightness out of range (0-7)")
        self._brightness = brightness

        self.clk.init(Pin.OUT, value=0)
        self.dio.init(Pin.OUT, value=0)
        sleep_us(self.TM1637_DELAY)

        self._write_data_cmd()
        self._write_dsp_ctrl()

    def _start(self):
        self.dio(0)
        sleep_us(self.TM1637_DELAY)
        self.clk(0)
        sleep_us(self.TM1637_DELAY)

    def _stop(self):
        self.dio(0)
        sleep_us(self.TM1637_DELAY)
        self.clk(1)
        sleep_us(self.TM1637_DELAY)
        self.dio(1)

    def _write_data_cmd(self):
        # automatic address increment, normal mode
        self._start()
        self._write_byte(self.TM1637_CMD1)
        self._stop()

    def _write_dsp_ctrl(self):
        # display on, set brightness
        self._start()
        self._write_byte(self.TM1637_CMD3 |
                         self.TM1637_DSP_ON | self._brightness)
        self._stop()

    def _write_byte(self, b):
        for i in range(8):
            self.dio((b >> i) & 1)
            sleep_us(self.TM1637_DELAY)
            self.clk(1)
            sleep_us(self.TM1637_DELAY)
            self.clk(0)
            sleep_us(self.TM1637_DELAY)
        self.clk(0)
        sleep_us(self.TM1637_DELAY)
        self.clk(1)
        sleep_us(self.TM1637_DELAY)
        self.clk(0)
        sleep_us(self.TM1637_DELAY)

    def brightness(self, val= None):
        """
        @desc: Get or set display brightness

        @args:
            - val: New brightness value (0-7), None to get current value

        @returns:
            - Current brightness if val is None
            - None if setting new brightness

        @note:
            - Brightness 0 = 1/16th pulse width
            - Brightness 7 = 14/16th pulse width

        @example:
            >>> segment.brightness(5)  # Set brightness
            >>> current = segment.brightness()  # Get current brightness
        """
        if val is None:
            return self._brightness
        if not 0 <= val <= 7:
            raise ValueError("Brightness out of range (0-7)")

        self._brightness = val
        self._write_data_cmd()
        self._write_dsp_ctrl()

    def _write(self, segments, pos=0):
        """
        @desc: Display up to 6 segments moving right from a given position.
               The MSB in the 2nd segment controls the colon between the 2nd and 3rd segments.
        """
        if not 0 <= pos <= 5:
            raise ValueError("Position out of range")
        self._write_data_cmd()
        self._start()

        self._write_byte(self.TM1637_CMD2 | pos)
        for seg in segments:
            self._write_byte(seg)
        self._stop()
        self._write_dsp_ctrl()

    def encode_digit(self, digit):
        """
        @desc: Convert a character 0-9, a-f to a segment.
        """
        return self._SEGMENTS[digit & 0x0f]

    def encode_string(self, string):
        """
        @desc: Convert an up to 4 character length string containing 0-9, a-z, space, dash, star to an array of segments, matching the length of the source string.
        """
        segments = bytearray(len(string))
        for i in range(len(string)):
            segments[i] = self.encode_char(string[i])
        return segments

    def encode_char(self, char):
        """
        @desc: Convert a character 0-9, a-z, space, dash or star to a segment.
        """
        o = ord(char)
        if o == 32:
            return self._SEGMENTS[36]  # space
        if o == 42:
            return self._SEGMENTS[38]  # star/degrees
        if o == 45:
            return self._SEGMENTS[37]  # dash
        if o >= 65 and o <= 90:
            return self._SEGMENTS[o-55]  # uppercase A-Z
        if o >= 97 and o <= 122:
            return self._SEGMENTS[o-87]  # lowercase a-z
        if o >= 48 and o <= 57:
            return self._SEGMENTS[o-48]  # 0-9
        raise ValueError(
            "Character out of range: {:d} '{:s}'".format(o, chr(o)))

    def show_hex(self, val: int) -> None:
        """
        @desc: Display hexadecimal value (0x0000 through 0xffff)

        @args:
            - val: Hexadecimal value to display

        @example:
            >>> segment.show_hex(0xA5)  # Displays "00A5"
            >>> segment.show_hex(0xFFFF)  # Displays "FFFF"
        """
        string = '{:04x}'.format(val & 0xffff)
        self._write(self.encode_string(string))

    def show_number(self, num: int) -> None:
        """
        @desc: Display numeric value (-999 through 9999)

        @args:
            - num: Number to display

        @note: Numbers outside range will be clamped

        @example:
            >>> segment.show_number(42)
            >>> segment.show_number(-123)
        """
        num = max(-999, min(num, 9999))
        string = '{0: >4d}'.format(num)
        self._write(self.encode_string(string))

    def show_numbers(self, num1: int, num2: int, colon: bool = True) -> None:
        """
        @desc: Display two numbers with optional colon separator

        @args:
            - num1: First number (-9 through 99)
            - num2: Second number (-9 through 99)
            - colon: Show colon between numbers (default: True)

        @example:
            >>> segment.show_numbers(12, 34)  # Shows "12:34"
            >>> segment.show_numbers(5, 30, False)  # Shows "0530"
        """
        num1 = max(-9, min(num1, 99))
        num2 = max(-9, min(num2, 99))
        segments = self.encode_string('{0:0>2d}{1:0>2d}'.format(num1, num2))
        if colon:
            segments[1] |= 0x80  # Enable colon
        self._write(segments)

    def show_temperature(self, num: float) -> None:
        """
        @desc: Display temperature with oC/oF
        @requires: led 7 segments module
        @args:
            - num: temperature value. Value in range [-9, 99] else display LO (low) or HI (high)
        @returns:
            None
        """
        if num < -9:
            self.show('lo')  # low
        elif num > 99:
            self.show('hi')  # high
        else:
            string = '{0: >2d}'.format(num)
            self._write(self.encode_string(string))
        self._write([self._SEGMENTS[38], self._SEGMENTS[12]], 2)  # degrees C

    def show(self, string: str, colon: bool = False) -> None:
        """
        @desc: Display text string with optional colon

        @args:
            - string: Text to display (up to 4 characters)
            - colon: Show colon after second digit (default: False)

        @example:
            >>> segment.show("Hi")
            >>> segment.show("12:34", True)
        """
        segments = self.encode_string(string)
        if len(segments) > 1 and colon:
            segments[1] |= 128
        self._write(segments[:4])

    def scroll(self, string: str, delay: int = 250) -> None:
        """
        @desc: Scroll text across the display

        @args:
            - string: Text to scroll
            - delay: Scroll speed in milliseconds (default: 250)

        @example:
            >>> segment.scroll("Hello World")
            >>> segment.scroll("Long text", 100)  # Faster scroll
        """
        segments = string if isinstance(string, list) else self.encode_string(string)
        data = [0] * 8
        data[4:0] = list(segments)
        for i in range(len(segments) + 5):
            self._write(data[0+i:4+i])
            sleep_ms(delay)

    def display(self, string: str) -> None:
        """
        @desc: Smart display of text (static or scrolling based on length)

        @args:
            - string: Text to display

        @note: 
            - Strings <= 4 chars: static display
            - Strings > 4 chars: automatic scrolling

        @example:
            >>> segment.display("Hi")  # Static display
            >>> segment.display("Hello World")  # Scrolling display
        """
        string = '{0: >4}'.format(string)
        if len(string) <= 4:
            self.show(string)
        else:
            self.scroll(string)

    def clear(self) -> None:
        """
        @desc: Clear all segments of the display

        @example:
            >>> segment.clear()
        """
        self._write([0, 0, 0, 0])
