# MicroPython SSD1306 OLED driver, I2C interface

from micropython import const
import framebuf
from machine import Pin, SoftI2C


class SSD1306(framebuf.FrameBuffer):
    """
    @desc: Base SSD1306 OLED display driver
           Provides low-level display control and graphics primitives

    @requires:
        - micropython.framebuf
        - machine.I2C
        - machine.Pin

    @version: 1.1.0
    """
    # Register definitions
    SET_CONTRAST = const(0x81)
    SET_ENTIRE_ON = const(0xA4)
    SET_NORM_INV = const(0xA6)
    SET_DISP = const(0xAE)
    SET_MEM_ADDR = const(0x20)
    SET_COL_ADDR = const(0x21)
    SET_PAGE_ADDR = const(0x22)
    SET_DISP_START_LINE = const(0x40)
    SET_SEG_REMAP = const(0xA0)
    SET_MUX_RATIO = const(0xA8)
    SET_COM_OUT_DIR = const(0xC0)
    SET_DISP_OFFSET = const(0xD3)
    SET_COM_PIN_CFG = const(0xDA)
    SET_DISP_CLK_DIV = const(0xD5)
    SET_PRECHARGE = const(0xD9)
    SET_VCOM_DESEL = const(0xDB)
    SET_CHARGE_PUMP = const(0x8D)

    def __init__(self, width, height, external_vcc, rotate_angle: int = 0):
        """
        @desc: Initialize base SSD1306 display

        @args:
            - width: Display width in pixels
            - height: Display height in pixels
            - external_vcc: Whether using external VCC
            - rotate_angle: Display rotation angle (default: 0)

        @example:
            >>> display = SSD1306(128, 64, False, 180)
        """
        self.width = width
        self.height = height
        self.external_vcc = external_vcc
        self.pages = self.height // 8
        self.buffer = bytearray(self.pages * self.width)
        self.rotate_angle = rotate_angle
        super().__init__(self.buffer, self.width, self.height, framebuf.MONO_VLSB)
        self.init_display()

    def init_display(self):
        """
        @desc: Initialize display with default configuration
               Sets up display timing, contrast, and orientation
        """
        for cmd in (
            self.SET_DISP | 0x00,  # off
            # address setting
            self.SET_MEM_ADDR,
            0x00,  # horizontal
            # resolution and layout
            self.SET_DISP_START_LINE | 0x00,
            self.SET_SEG_REMAP | 0x01,  # column addr 127 mapped to SEG0
            self.SET_MUX_RATIO,
            self.height - 1,
            self.SET_COM_OUT_DIR | 0x08,  # scan from COM[N] to COM0
            self.SET_DISP_OFFSET,
            0x00,
            self.SET_COM_PIN_CFG,
            0x02 if self.width > 2 * self.height else 0x12,
            # timing and driving scheme
            self.SET_DISP_CLK_DIV,
            0x80,
            self.SET_PRECHARGE,
            0x22 if self.external_vcc else 0xF1,
            self.SET_VCOM_DESEL,
            0x30,  # 0.83*Vcc
            # display
            self.SET_CONTRAST,
            0xFF,  # maximum
            self.SET_ENTIRE_ON,  # output follows RAM contents
            self.SET_NORM_INV,  # not inverted
            # charge pump
            self.SET_CHARGE_PUMP,
            0x10 if self.external_vcc else 0x14,
            self.SET_DISP | 0x01,
        ):  # on
            self.write_cmd(cmd)
        self.fill(0)
        self.rotate(self.rotate_angle)
        self.show()

    def poweroff(self):
        self.write_cmd(self.SET_DISP | 0x00)

    def poweron(self):
        self.write_cmd(self.SET_DISP | 0x01)

    def contrast(self, contrast):
        self.write_cmd(self.SET_CONTRAST)
        self.write_cmd(contrast)

    def invert(self, invert):
        self.write_cmd(self.SET_NORM_INV | (invert & 1))

    def rotate(self, rotate):
        self.write_cmd(self.SET_COM_OUT_DIR | ((rotate & 1) << 3))
        self.write_cmd(self.SET_SEG_REMAP | (rotate & 1))

    def show(self):
        """
        @desc: Update display with current buffer contents

        @example:
            >>> oled.text("Hello", 0, 0, 1)
            >>> oled.show()  # Display the text
        """
        x0 = 0
        x1 = self.width - 1
        if self.width == 64:
            x0 += 32
            x1 += 32
        self.write_cmd(self.SET_COL_ADDR)
        self.write_cmd(x0)
        self.write_cmd(x1)
        self.write_cmd(self.SET_PAGE_ADDR)
        self.write_cmd(0)
        self.write_cmd(self.pages - 1)
        self.write_data(self.buffer)

    def clear(self):
        """
        @desc: Set all pixels with 0 value
        @requires: Oled module
        @args:
            None
        @return:
            None
        """
        self.fill(0)


# I2C mode
class Oled(SSD1306):
    """
    @desc: BeE board OLED display driver
           Provides high-level display control for BeE board
           Supports text display, rotation, and screen clearing

    @example:
        >>> from BeeBrain import bee
        >>> oled = Oled(port=bee.PORT1, rotation=180)
        >>> oled.clear()
        >>> oled.text("Hello BeE board!", 0, 0, 1)
        >>> oled.show()

    @version: 1.0.0
    """

    HEART = [
        "  ##  ## ",
        " ######## ",
        " ######## ",
        "  ###### ",
        "   ####  ",
        "    ##   "
    ]

    SMILE = [
        "    ##    ",
        "  #    #  ",
        " #  ##  # ",
        " #  ##  # ",
        "  #    #  ",
        "    ##    "
    ]

    SAD = [
        "    ##    ",
        "  #    #  ",
        " #  ##  # ",
        " #  ##  # ",
        "  #    #  ",
        "    ##    "
    ]

    

    def __init__(self, 
                 port = None, 
                 rotation: int = 0, 
                 timeout = 1000000):
        """
        @desc: Initialize OLED display with BeE board port

        @args:
            - port: BeE board port object
            - rotation: Display rotation in degrees (default: 0)

        @raises:
            - ValueError: If I2C initialization fails

        @example:
            >>> from BeeBrain import bee
            >>> oled = Oled(port=bee.PORT1, rotation=180)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.i2c = SoftI2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)), timeout=timeout)
        self.addr = 0x3C
        self.external_vcc = False
        self.temp = bytearray(2)
        self.rotation = rotation
        self.write_list = [b"\x40", None]  # Co=0, D/C#=1
        super().__init__(128, 64, self.external_vcc, self.rotation)

    def write_cmd(self, cmd):
        """
        @desc: Write command to display controller
        
        @args:
            - cmd: Command byte to send
        """
        self.temp[0] = 0x80  # Co=1, D/C#=0
        self.temp[1] = cmd
        self.i2c.writeto(self.addr, self.temp)

    def write_data(self, buf):
        """
        @desc: Write display data to controller
        
        @args:
            - buf: Data buffer to send
        """
        self.write_list[1] = buf
        self.i2c.writevto(self.addr, self.write_list)

    def long_text(self, text: str = ""):
        """
        @desc: Display long text with automatic line wrapping

        @args:
            - text: String to display

        @example:
            >>> oled.long_text("This is a long text that will be automatically wrapped")
        """
        self.clear()
        max_char = 16
        max_line = 10
        text_array = [text[i:i+max_char]
                     for i in range(0, len(text), max_char)]
        for i in range(0, len(text_array)):
            self.text(text_array[i], 0, i * max_line, 1)
            if i > (64 / max_line):
                break
        self.show()

    def write(self, text, x: int = 0, y: int = 0, color: int = 1):
        """
        @desc: Write text at specified position

        @args:
            - text: Text to display
            - x: X coordinate (default: 0)
            - y: Y coordinate (default: 0)
            - color: Pixel color (1=on, 0=off, default: 1)

        @example:
            >>> oled.write("Hello", 10, 20)
        """
        self.text(str(text), x, y, color)
        self.show()

    def clear(self):
        """
        @desc: Clear display and buffer
        
        @example:
            >>> oled.clear()
            >>> oled.show()
        """
        self.fill(0)

    def display_image(self, image_data, x=0, y=0):
        """
        @desc: Display a monochrome image on the OLED
        
        @args:
            - image_data: Bytearray containing the image data
            - x: X coordinate (default: 0)
            - y: Y coordinate (default: 0)
            
        @example:
            >>> # For a 32x32 image
            >>> oled.display_image(image_data, 48, 16)
            >>> oled.show()
        """
        for y_pos, row in enumerate(image_data):
            if y + y_pos >= self.height:
                break
            for x_pos, pixel in enumerate(row):
                if x + x_pos >= self.width:
                    break
                if pixel:
                    self.pixel(x + x_pos, y + y_pos, 1)
        self.show()

    def load_pbm(self, pbm_data):
        """
        @desc: Load and display a PBM (Portable Bitmap) format image
        
        @args:
            - pbm_data: String or bytearray containing PBM data
        
        @example:
            >>> pbm = '''P1
            ... # Example bitmap
            ... 8 8
            ... 0 1 0 1 0 1 0 1
            ... 1 0 1 0 1 0 1 0
            ... 0 1 0 1 0 1 0 1
            ... 1 0 1 0 1 0 1 0
            ... 0 1 0 1 0 1 0 1
            ... 1 0 1 0 1 0 1 0
            ... 0 1 0 1 0 1 0 1
            ... 1 0 1 0 1 0 1 0'''
            >>> oled.load_pbm(pbm)
        """
        self.clear()
        lines = pbm_data.strip().split('\n')
        
        # Skip comments
        i = 1
        while lines[i].startswith('#'):
            i += 1
        
        # Get dimensions
        width, height = map(int, lines[i].split())
        i += 1
        
        # Center the image
        x_offset = (self.width - width) // 2
        y_offset = (self.height - height) // 2
        
        # Parse pixel data
        y = 0
        for line in lines[i:]:
            if y >= height:
                break
            pixels = line.split()
            for x, pixel in enumerate(pixels):
                if x >= width:
                    break
                if pixel == '1':
                    self.pixel(x_offset + x, y_offset + y, 1)
            y += 1
        self.show()
        
    def draw_icon(self, icon, x=0, y=0):
        """
        @desc: Draw a small icon defined as a list of rows
        
        @args:
            - icon: List of strings representing rows of pixels
            - x: X coordinate (default: 0)
            - y: Y coordinate (default: 0)
            
        @example:
            >>> heart = [
            ...     "  ##  ## ",
            ...     " ######## ",
            ...     " ######## ",
            ...     "  ###### ",
            ...     "   ####  ",
            ...     "    ##   "
            ... ]
            >>> oled.draw_icon(heart, 60, 30)
            >>> oled.show()
        """
        for y_pos, row in enumerate(icon):
            for x_pos, char in enumerate(row):
                if char != ' ':
                    self.pixel(x + x_pos, y + y_pos, 1)
        self.show()
    
    def draw_pixel(self, x, y, color=1):
        """
        @desc: Draw a single pixel at (x, y) with specified color
        
        @args:
            - x: X coordinate
            - y: Y coordinate
            - color: Pixel color (1=on, 0=off, default: 1)
            
        @example:
            >>> oled.draw_pixel(10, 10, 1)
            >>> oled.show()
        """
        self.pixel(x, y, color)
        self.show()
    
    def draw_line(self, x1, y1, x2, y2, color=1):
        """
        @desc: Draw a line between two points
        
        @args:
            - x1: X coordinate of first point
            - y1: Y coordinate of first point
            - x2: X coordinate of second point
            - y2: Y coordinate of second point
            - color: Pixel color (1=on, 0=off, default: 1)
            
        @example:
            >>> oled.draw_line(0, 0, 127, 63, 1)
            >>> oled.show()
        """
        self.line(x1, y1, x2, y2, color)
        self.show()

    def draw_circle(self, x, y, r, color=1):
        """
        @desc: Draw a circle with center (x, y) and radius r
        
        @args:
            - x: X coordinate of center
            - y: Y coordinate of center
            - r: Radius of circle
            - color: Pixel color (1=on, 0=off, default: 1)
            
        @example:
            >>> oled.draw_circle(64, 32, 20, 1)
            >>> oled.show()
        """
        self.ellipse(x, y, r, r, color)
        self.show()
    
    def draw_rect(self, x, y, w, h, color=1):
        """
        @desc: Draw a rectangle with top-left corner at (x, y) and width w and height h
        
        @args:
            - x: X coordinate of top-left corner
            - y: Y coordinate of top-left corner
            - w: Width of rectangle
            - h: Height of rectangle
            - color: Pixel color (1=on, 0=off, default: 1)
            
        @example:
            >>> oled.draw_rect(10, 10, 20, 30, 1)
            >>> oled.show()
        """
        self.rect(x, y, w, h, color)
        self.show()
