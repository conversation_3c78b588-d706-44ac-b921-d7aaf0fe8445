from machine import Pin


class DigitalIO:

    """
    @desc: Digital I/O interface for BeE board
           Supports basic on/off control and value reading

    @example:
        >>> from BeeBrain import bee
        >>> led = DigitalIO(bee.PORT1)
        >>> led.on()
        >>> led.off()
        >>> print(led.value)
    """

    HIGH = 1
    LOW = 0

    def __init__(self, port = None):
        """
        @desc: Initialize digital I/O pin

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - pin: GPIO pin number

        @example:
            >>> from BeeBrain import bee
            >>> led = DigitalIO(bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__pin = Pin(port.use_pins(1), Pin.OUT)

    def high(self):
        """
        @desc: Set pin to high (1) level

        @example:
            >>> led.high()
        """
        self.__pin.on()

    def on(self):
        """
        @desc: Set pin to high (1) level

        @example:
            >>> led.on()
        """
        self.__pin.on()

    def low(self):
        """
        @desc: Set pin to low (0) level

        @example:
            >>> led.low()
        """
        self.__pin.off()

    def off(self):
        """
        @desc: Set pin to low (0) level

        @example:
            >>> led.off()
        """
        self.__pin.off()

    def set(self, value):
        """
        @desc: Set pin to specified value

        @args:
            - value: Value to set (0/1, True/False)

        @example:
            >>> led.set(1)
            >>> led.set(True)
        """
        if value == self.HIGH:
            self.on()
        else:
            self.off()

    @property
    def value(self):
        """
        @desc: Get current pin value

        @returns:
            - int: Current pin value (0 or 1)

        @example:
            >>> state = led.value
            >>> print(f"Pin state: {state}")
        """
        return self.__pin.value()