from machine import Pin, I2C

class LineDetect:
    """
    @desc: Interface for PCF8574-based line detection module
           Supports 4 infrared sensors for line following applications

    @example:
        >>> from BeeBrain import bee
        >>> sensor = LineDetect(port=bee.PORT1)
        >>> if sensor.pin(sensor.IR1):
        ...     print("Line detected on IR1")
        >>> values = sensor.read_all()  # Read all sensors

    @version: 1.1.0
    """

    # Sensor pin definitions
    IR1 = 0  # Left-most sensor
    IR2 = 1  # Left-center sensor
    IR3 = 2  # Right-center sensor
    IR4 = 3  # Right-most sensor

    def __init__(self, port = None, address: int = 0x23):
        """
        @desc: Initialize line detection module with specified port and I2C address

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - address: I2C address of PCF8574 (default: 0x23)

        @raises:
            - OSError: If device not found at specified I2C address

        @example:
            >>> from BeeBrain import bee
            >>> sensor = LineDetect(port=bee.PORT1)  # Default address
            >>> sensor = LineDetect(port=bee.PORT2, address=0x24)  # Custom address
        """
        if port is None:
            raise ValueError("Invalid port")
        self._i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)))
        self._address = address
        self._port = bytearray(1)

    def check(self) -> bool:
        """
        @desc: Verify if line detection module is present and responding

        @returns:
            - bool: True if device is found and responding

        @raises:
            - OSError: If device not found at configured address

        @example:
            >>> if sensor.check():
            ...     print("Line detector ready")
        """
        if self._i2c.scan().count(self._address) == 0:
            raise OSError(
                f"LineDetect not found at I2C address {self._address:#x}")
        return True

    @property
    def port(self) -> int:
        """
        @desc: Get current state of all sensor pins (bits 4-7)

        @returns:
            - int: Current state of sensor pins

        @note: Returns only upper 4 bits (pins 4-7) of port value

        @example:
            >>> state = sensor.port
            >>> print(f"Port state: {state:08b}")
        """
        self._read()
        return self._port[0] >> 4

    def pin(self, pin: int, value = None):
        """
        @desc: Read or write individual sensor pin state

        @args:
            - pin: Pin number (0-7)
            - value: Optional boolean value to write (None for read)

        @returns:
            - int: Current pin state if reading
            - None: If writing pin state

        @raises:
            - ValueError: If pin number is invalid

        @example:
            >>> state = sensor.pin(sensor.IR1)  # Read IR1
            >>> sensor.pin(sensor.IR2, True)    # Set IR2 high
        """
        pin = self._validate_pin(pin)
        if value is None:
            self._read()
            return (self._port[0] >> pin) & 1
        if value:
            self._port[0] |= 1 << pin
        else:
            self._port[0] &= ~(1 << pin)
        self._write()

    def read_all(self) -> list[int]:
        """
        @desc: Read state of all four IR sensors

        @returns:
            - list[int]: List of 4 sensor states [IR1, IR2, IR3, IR4]

        @example:
            >>> states = sensor.read_all()
            >>> print(f"Sensor states: {states}")
        """
        self._read()
        return [
            (self._port[0] >> self.IR1) & 1,
            (self._port[0] >> self.IR2) & 1,
            (self._port[0] >> self.IR3) & 1,
            (self._port[0] >> self.IR4) & 1
        ]

    def _validate_pin(self, pin: int) -> int:
        """
        @desc: Validate pin number is within valid range

        @args:
            - pin: Pin number to validate

        @returns:
            - int: Validated pin number

        @raises:
            - ValueError: If pin number is outside valid range (0-7)
        """
        if not 0 <= pin <= 7:
            raise ValueError(f"Invalid pin {pin}. Use 0-7.")
        return pin

    def _read(self) -> None:
        """
        @desc: Read current state from PCF8574 device
        """
        self._i2c.readfrom_into(self._address, self._port)

    def _write(self) -> None:
        """
        @desc: Write current state to PCF8574 device
        """
        self._i2c.writeto(self._address, self._port)
