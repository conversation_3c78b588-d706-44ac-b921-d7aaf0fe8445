
import math
import time
from machine import Pin, PWM
from micropython import const


class Motor:
    """
    @desc: Control TB6612 DC motors and servos directly from ESP32 S3
    @requires: BeeBrain module
    """
    
    __FORWARD = const(0)
    __BACKWARD = const(1)
    __LEFT = const(2)
    __RIGHT = const(3)
    
    __DC_FREQUENCY = const(3000)
    
    def __init__(self, 
                 ain1_pin=41, ain2_pin=42, pwma_pin=39,
                 bin1_pin=35, bin2_pin=36, pwmb_pin=40):
        """
        @desc: Initialize TB6612 motor driver
        @args:
            - ain1_pin, ain2_pin, pwma_pin: Motor A control pins
            - bin1_pin, bin2_pin, pwmb_pin: Motor B control pins
        """
        # Motor A pins
        self.__ain1 = Pin(ain1_pin, Pin.OUT)
        self.__ain2 = Pin(ain2_pin, Pin.OUT)
        self.__pwma = PWM(Pin(pwma_pin), freq=__DC_FREQUENCY)
        
        # Motor B pins
        self.__bin1 = Pin(bin1_pin, Pin.OUT)
        self.__bin2 = Pin(bin2_pin, Pin.OUT)
        self.__pwmb = PWM(Pin(pwmb_pin), freq=__DC_FREQUENCY)
        
        # Initialize motors to stopped state
        self.brake(0)
        self.brake(1)
    
    def speed(self, motor_index: int, power: int) -> None:
        """
        @desc: Set speed of motor, max speed is 50%
        @args:
            - motor_index: 0 for motor A, 1 for motor B
            - power: -100 to 100 (negative for reverse)
        """
        # Limit power to 50% max
        if power > 50:
            power = 50
        elif power < -50:
            power = -50
            
        duty = int(abs(power) / 100.0 * 1023)
        
        if motor_index == 0:  # Motor A
            if power >= 0:
                self.__ain1.value(1)
                self.__ain2.value(0)
            else:
                self.__ain1.value(0)
                self.__ain2.value(1)
            self.__pwma.duty(duty)
            
        elif motor_index == 1:  # Motor B
            if power >= 0:
                self.__bin1.value(1)
                self.__bin2.value(0)
            else:
                self.__bin1.value(0)
                self.__bin2.value(1)
            self.__pwmb.duty(duty)
    
    def brake(self, motor_index: int) -> None:
        """
        @desc: Brake motor
        @args:
            - motor_index: 0 for motor A, 1 for motor B
        """
        if motor_index == 0:  # Motor A
            self.__ain1.value(0)
            self.__ain2.value(0)
            self.__pwma.duty(0)
        elif motor_index == 1:  # Motor B
            self.__bin1.value(0)
            self.__bin2.value(0)
            self.__pwmb.duty(0)


class Servo:
    """
    @desc: Control servos directly from ESP32 S3
    @requires: BeeBrain module
    """
    
    __SERVO_FREQUENCY = const(50)
    __MIN_US = const(600)
    __MAX_US = const(2400)
    __DEGREES = const(270)
    
    def __init__(self, s1_pin=37, s2_pin=38, s3_pin=47, s4_pin=21):
        """
        @desc: Initialize servo control
        @args:
            - s1_pin, s2_pin, s3_pin, s4_pin: Servo control pins
        """
        self.__servos = [
            PWM(Pin(s1_pin), freq=__SERVO_FREQUENCY),
            PWM(Pin(s2_pin), freq=__SERVO_FREQUENCY),
            PWM(Pin(s3_pin), freq=__SERVO_FREQUENCY),
            PWM(Pin(s4_pin), freq=__SERVO_FREQUENCY)
        ]
        self.__degrees = __DEGREES
        
        # Calculate duty cycle range
        self.__period_us = 1000000 // __SERVO_FREQUENCY
        self.__min_duty = int(65535 * __MIN_US / self.__period_us)
        self.__max_duty = int(65535 * __MAX_US / self.__period_us)
    
    def position(self, index: int, degrees=None, radians=None, us=None, duty=None) -> None:
        """
        @desc: Set position of servo
        @args:
            - index: servo index [0-3]
            - degrees: set degree of servo [0-270]
            - radians, us, duty: alternative position methods
        """
        if not 0 <= index <= 3:
            raise ValueError("Servo index must be 0-3")
            
        span = self.__max_duty - self.__min_duty
        
        if degrees is not None:
            duty = self.__min_duty + span * degrees / self.__degrees
        elif radians is not None:
            duty = self.__min_duty + span * radians / math.radians(self.__degrees)
        elif us is not None:
            duty = int(65535 * us / self.__period_us)
        elif duty is not None:
            pass
        else:
            return
            
        duty = min(self.__max_duty, max(self.__min_duty, int(duty)))
        self.__servos[index].duty_u16(duty)
    
    def release(self, index: int):
        """
        @desc: Release servo motor
        @args:
            - index: index of servo [0-3]
        """
        if 0 <= index <= 3:
            self.__servos[index].duty_u16(0)

