from micropython import const
from machine import I2C, Pin
from time import sleep


class HDC1080:
    """
    @desc: HDC1080 temperature and humidity sensor interface for BeE board
           Supports temperature and humidity readings with configurable resolution

    @example:
        >>> from BeeBrain import bee
        >>> sensor = HDC1080(port=bee.PORT1)
        >>> temp = sensor.temperature
        >>> humi = sensor.humidity
        >>> print(f"Temperature: {temp}C, Humidity: {humi}%")
    """
    TEMP_REG = const(0x00)  # temperature register
    HUMI_REG = const(0x01)  # humidity register
    CONF_REG = const(0x02)  # configuration register
    FSER_REG = const(0xFB)  # first two bytes of serial ID register
    MSER_REG = const(0xFC)  # middle two bytes of serial ID register
    LSER_REG = const(0xFD)  # last two bytes of serial ID register
    MFID_REG = const(0xFE)  # manufacturer ID register
    DVID_REG = const(0xFF)  # device ID register

    def __init__(self, port = None, slave_addr = 64):
        """
        @desc: Initialize HDC1080 sensor with specified port and slave address

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - slave_addr: I2C slave address of HDC1080 (default: 64)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = HDC1080(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)))
        self.addr = slave_addr
        self.fmt = '>2B'
        setup_data = 1 << 4
        data = bytearray(3)
        data[0] = CONF_REG
        data[1] = setup_data
        self.i2c.writeto(self.addr, data)

    @property
    def temperature(self, celsius=True):
        """
        @desc: Read temperature from HDC1080 sensor

        @args:
            - celsius: True to return Celsius, False for Fahrenheit

        @returns:
            - float: Temperature in Celsius or Fahrenheit

        @example:
            >>> temp = sensor.temperature
            >>> print(f"Temperature: {temp}C")
        """
        data = bytearray([TEMP_REG])
        self.i2c.writeto(self.addr, data)
        sleep(0.0635)
        data = self.i2c.readfrom(self.addr, 2)
        value = int.from_bytes(data, "big")
        if celsius:
            value = (value / (2 ** 16)) * 165 - 40
        else:
            value = (1.8 * value / (2 ** 16)) * 165 - 40
        return value

    @property
    def humidity(self):
        """
        @desc: Read humidity from HDC1080 sensor

        @returns:
            - float: Humidity in percent

        @example:
            >>> humi = sensor.humidity
            >>> print(f"Humidity: {humi}%")
        """
        data = bytearray([HUMI_REG])
        self.i2c.writeto(self.addr, data)
        sleep(0.065)
        data = self.i2c.readfrom(self.addr, 2)
        value = int.from_bytes(data, "big")
        return (value / (2 ** 16)) * 100