# https://github.com/micropython-IMU/micropython-mpu9150.git

from math import sqrt, degrees, acos, atan2
from utime import sleep_ms
from machine import SoftI2C, Pin

import math
import time


class MPUException(OSError):
    '''
    Exception for MPU devices
    '''
    pass


def bytes_toint(msb, lsb):
    '''
    Convert two bytes to signed integer (big endian)
    for little endian reverse msb, lsb arguments
    Can be used in an interrupt handler
    '''
    if not msb & 0x80:
        return msb << 8 | lsb  # +ve
    return - (((msb ^ 255) << 8) | (lsb ^ 255) + 1)


def default_wait():
    '''
    delay of 50 ms
    '''
    sleep_ms(50)


class Vector3d(object):
    '''
    Represents a vector in a 3D space using Cartesian coordinates.
    Internally uses sensor relative coordinates.
    Returns vehicle-relative x, y and z values.
    '''

    def __init__(self, transposition, scaling, update_function):
        self._vector = [0, 0, 0]
        self._ivector = [0, 0, 0]
        self.cal = (0, 0, 0)
        self.argcheck(transposition, "Transposition")
        self.argcheck(scaling, "Scaling")
        if set(transposition) != {0, 1, 2}:
            raise ValueError(
                'Transpose indices must be unique and in range 0-2')
        self._scale = scaling
        self._transpose = transposition
        self.update = update_function

    def argcheck(self, arg, name):
        '''
        checks if arguments are of correct length
        '''
        if len(arg) != 3 or not (type(arg) is list or type(arg) is tuple):
            raise ValueError(name + ' must be a 3 element list or tuple')

    def calibrate(self, stopfunc, waitfunc=default_wait):
        '''
        calibration routine, sets cal
        '''
        self.update()
        # Initialise max and min lists with current values
        maxvec = self._vector[:]
        minvec = self._vector[:]
        while not stopfunc():
            waitfunc()
            self.update()
            maxvec = list(map(max, maxvec, self._vector))
            minvec = list(map(min, minvec, self._vector))
        self.cal = tuple(map(lambda a, b: (a + b)/2, maxvec, minvec))

    @property
    def _calvector(self):
        '''
        Vector adjusted for calibration offsets
        '''
        return list(map(lambda val, offset: val - offset, self._vector, self.cal))

    @property
    def x(self):                                # Corrected, vehicle relative floating point values
        self.update()
        return self._calvector[self._transpose[0]] * self._scale[0]

    @property
    def y(self):
        self.update()
        return self._calvector[self._transpose[1]] * self._scale[1]

    @property
    def z(self):
        self.update()
        return self._calvector[self._transpose[2]] * self._scale[2]

    @property
    def xyz(self):
        self.update()
        return (self._calvector[self._transpose[0]] * self._scale[0],
                self._calvector[self._transpose[1]] * self._scale[1],
                self._calvector[self._transpose[2]] * self._scale[2])

    @property
    def magnitude(self):
        x, y, z = self.xyz  # All measurements must correspond to the same instant
        return sqrt(x**2 + y**2 + z**2)

    @property
    def inclination(self):
        x, y, z = self.xyz
        return degrees(acos(z / sqrt(x**2 + y**2 + z**2)))

    @property
    def elevation(self):
        return 90 - self.inclination

    @property
    def azimuth(self):
        x, y, z = self.xyz
        return degrees(atan2(y, x))

    # Raw uncorrected integer values from sensor
    @property
    def ix(self):
        return self._ivector[0]

    @property
    def iy(self):
        return self._ivector[1]

    @property
    def iz(self):
        return self._ivector[2]

    @property
    def ixyz(self):
        return self._ivector

    @property
    def transpose(self):
        return tuple(self._transpose)

    @property
    def scale(self):
        return tuple(self._scale)


class MPU6050:
    """
    @desc: Interface for MPU6050 6-axis motion tracking device
           Provides accelerometer and gyroscope measurements with configurable ranges
           Supports motion detection, orientation calculation, and calibration

    @requires: 
        - machine.I2C
        - machine.Pin
        - utime.sleep_ms
        - math

    @example:
        >>> from BeeBrain import bee
        >>> imu = MPU6050(port=bee.PORT1)
        >>> accel_x, accel_y, accel_z = imu.accel.xyz
        >>> gyro_x, gyro_y, gyro_z = imu.gyro.xyz
        >>> pitch = imu.pitchDeg
        >>> roll = imu.rollDeg
        >>> yaw = imu.yawDeg
        >>> if imu.is_shaking():
        ...     print("Device is shaking!")

    @version: 1.0.0
    """

    _I2Cerror = "I2C failure when communicating with IMU"
    _mpu_addr = (104, 105)  # addresses of MPU9150/MPU6050
    _chip_id = 104

    def __init__(self, 
                 port = None, 
                 device_addr = None, 
                 transposition = (0, 1, 2), 
                 scaling = (1, 1, 1), 
                 timeout = 1000000):
        """
        @desc: Initialize MPU6050 sensor with specified configuration

        @args:
            - port: BeE board port object
            - device_addr: Optional I2C address (None for auto-detection)
            - transposition: Axis mapping tuple (default: (0,1,2))
            - scaling: Scaling factors for each axis (default: (1,1,1))

        @raises:
            - MPUException: If I2C communication fails
            - ValueError: If device address or I2C configuration is invalid

        @example:
            >>> from BeeBrain import bee
            >>> imu = MPU6050(port=bee.PORT1)  # Default configuration
            >>> imu = MPU6050(port=bee.PORT2, device_addr=0)  # Specific address
        """
        if port is None:
            raise ValueError("Invalid port")
        self._accel = Vector3d(transposition, scaling, self._accel_callback)
        self._gyro = Vector3d(transposition, scaling, self._gyro_callback)

        side_str = SoftI2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)), timeout=timeout)

        self.pitchDeg = 0
        self.rollDeg = 0
        self._yawDeg = 0
        
        # Add variables for yaw calculation
        self._last_update = time.ticks_ms()
        self._yaw = 0
        
        # Add yaw ratio
        self._yaw_ratio = 0.55
        
        # Add variables for shake detection
        self._last_accel = (0, 0, 0)
        self._shake_threshold = 0.2  # Adjustable threshold for shake detection (in g)
        
        # Pre-allocated buffers for reads
        self.buf1 = bytearray(1)
        self.buf2 = bytearray(2)
        self.buf3 = bytearray(3)
        self.buf6 = bytearray(6)

        # Initialize I2C communication
        sleep_ms(200)  # Allow device to settle
        if isinstance(side_str, str):
            self._mpu_i2c = I2C(side_str)
        elif hasattr(side_str, 'readfrom'):
            self._mpu_i2c = side_str
        else:
            raise ValueError("Invalid I2C instance")

        # Set device address
        if device_addr is None:
            devices = set(self._mpu_i2c.scan())
            mpus = devices.intersection(set(self._mpu_addr))
            number_of_mpus = len(mpus)
            if number_of_mpus == 0:
                raise MPUException("No MPU's detected")
            elif number_of_mpus == 1:
                self.mpu_addr = mpus.pop()
            else:
                raise ValueError(
                    "Two MPU's detected: must specify a device address")
        else:
            if device_addr not in (0, 1):
                raise ValueError('Device address must be 0 or 1')
            self.mpu_addr = self._mpu_addr[device_addr]

        # Test communication by reading chip_id: throws exception on error
        self.chip_id
        # Can communicate with chip. Set it up.
        self.wake()                             # wake it up
        self.passthrough = True                 # Enable mag access from main I2C bus
        self.accel_range = 0                    # default to highest sensitivity
        self.gyro_range = 0                     # Likewise for gyro

    # read from device
    # addr = I2C device address, memaddr = memory location within the I2C device
    def _read(self, buf, memaddr, addr):
        '''
        Read bytes to pre-allocated buffer Caller traps OSError.
        '''
        self._mpu_i2c.readfrom_mem_into(addr, memaddr, buf)

    # write to device
    def _write(self, data, memaddr, addr):
        '''
        Perform a memory write. Caller should trap OSError.
        '''
        self.buf1[0] = data
        self._mpu_i2c.writeto_mem(addr, memaddr, self.buf1)

    # wake
    def wake(self):
        '''
        Wakes the device.
        '''
        try:
            self._write(0x01, 0x6B, self.mpu_addr)  # Use best clock source
        except OSError:
            raise MPUException(self._I2Cerror)
        return 'awake'

    # mode
    def sleep(self):
        '''
        Sets the device to sleep mode.
        '''
        try:
            self._write(0x40, 0x6B, self.mpu_addr)
        except OSError:
            raise MPUException(self._I2Cerror)
        return 'asleep'

    # chip_id
    @property
    def chip_id(self):
        '''
        Returns Chip ID
        '''
        try:
            self._read(self.buf1, 0x75, self.mpu_addr)
        except OSError:
            raise MPUException(self._I2Cerror)
        chip_id = int(self.buf1[0])
        if chip_id != self._chip_id:
            raise ValueError(
                'Bad chip ID retrieved: MPU communication failure')
        return chip_id

    @property
    def sensors(self):
        """
        @desc: Get accelerometer and gyroscope sensor objects

        @returns: Tuple(Vector3d, Vector3d) - (accelerometer, gyroscope)

        @example:
            >>> accel, gyro = imu.sensors
            >>> print(f"Acceleration: {accel.xyz}")
        """
        return self._accel, self._gyro

    # get temperature
    @property
    def temperature(self):
        """
        @desc: Get current temperature reading from sensor

        @returns: float - Temperature in degrees Celsius

        @raises: MPUException - If I2C communication fails

        @example:
            >>> temp = imu.temperature
            >>> print(f"Temperature: {temp}°C")
        """
        try:
            self._read(self.buf2, 0x41, self.mpu_addr)
            return bytes_toint(self.buf2[0], self.buf2[1])/340 + 35
        except OSError:
            raise MPUException(self._I2Cerror)

    # passthrough
    @property
    def passthrough(self):
        '''
        Returns passthrough mode True or False
        '''
        try:
            self._read(self.buf1, 0x37, self.mpu_addr)
            return self.buf1[0] & 0x02 > 0
        except OSError:
            raise MPUException(self._I2Cerror)

    @passthrough.setter
    def passthrough(self, mode):
        '''
        Sets passthrough mode True or False
        '''
        if type(mode) is bool:
            val = 2 if mode else 0
            try:
                self._write(val, 0x37, self.mpu_addr)  # I think this is right.
                self._write(0x00, 0x6A, self.mpu_addr)
            except OSError:
                raise MPUException(self._I2Cerror)
        else:
            raise ValueError('pass either True or False')

    # sample rate. Not sure why you'd ever want to reduce this from the default.
    @property
    def sample_rate(self):
        '''
        Get sample rate as per Register Map document section 4.4
        SAMPLE_RATE= Internal_Sample_Rate / (1 + rate)
        default rate is zero i.e. sample at internal rate.
        '''
        try:
            self._read(self.buf1, 0x19, self.mpu_addr)
            return self.buf1[0]
        except OSError:
            raise MPUException(self._I2Cerror)

    @sample_rate.setter
    def sample_rate(self, rate):
        '''
        Set sample rate as per Register Map document section 4.4
        '''
        if rate < 0 or rate > 255:
            raise ValueError("Rate must be in range 0-255")
        try:
            self._write(rate, 0x19, self.mpu_addr)
        except OSError:
            raise MPUException(self._I2Cerror)

    # Low pass filters. Using the filter_range property of the MPU9250 is
    # harmless but gyro_filter_range is preferred and offers an extra setting.
    @property
    def filter_range(self):
        '''
        Returns the gyro and temperature sensor low pass filter cutoff frequency
        Pass:               0   1   2   3   4   5   6
        Cutoff (Hz):        250 184 92  41  20  10  5
        Sample rate (KHz):  8   1   1   1   1   1   1
        '''
        try:
            self._read(self.buf1, 0x1A, self.mpu_addr)
            res = self.buf1[0] & 7
        except OSError:
            raise MPUException(self._I2Cerror)
        return res

    @filter_range.setter
    def filter_range(self, filt):
        '''
        Sets the gyro and temperature sensor low pass filter cutoff frequency
        Pass:               0   1   2   3   4   5   6
        Cutoff (Hz):        250 184 92  41  20  10  5
        Sample rate (KHz):  8   1   1   1   1   1   1
        '''
        # set range
        if filt in range(7):
            try:
                self._write(filt, 0x1A, self.mpu_addr)
            except OSError:
                raise MPUException(self._I2Cerror)
        else:
            raise ValueError('Filter coefficient must be between 0 and 6')

    # accelerometer range
    @property
    def accel_range(self):
        '''
        Accelerometer range
        Value:              0   1   2   3
        for range +/-:      2   4   8   16  g
        '''
        try:
            self._read(self.buf1, 0x1C, self.mpu_addr)
            ari = self.buf1[0]//8
        except OSError:
            raise MPUException(self._I2Cerror)
        return ari

    @accel_range.setter
    def accel_range(self, accel_range):
        '''
        Set accelerometer range
        Pass:               0   1   2   3
        for range +/-:      2   4   8   16  g
        '''
        ar_bytes = (0x00, 0x08, 0x10, 0x18)
        if accel_range in range(len(ar_bytes)):
            try:
                self._write(ar_bytes[accel_range], 0x1C, self.mpu_addr)
            except OSError:
                raise MPUException(self._I2Cerror)
        else:
            raise ValueError('accel_range can only be 0, 1, 2 or 3')

    # gyroscope range
    @property
    def gyro_range(self):
        '''
        Gyroscope range
        Value:              0   1   2    3
        for range +/-:      250 500 1000 2000  degrees/second
        '''
        # set range
        try:
            self._read(self.buf1, 0x1B, self.mpu_addr)
            gri = self.buf1[0]//8
        except OSError:
            raise MPUException(self._I2Cerror)
        return gri

    @gyro_range.setter
    def gyro_range(self, gyro_range):
        '''
        Set gyroscope range
        Pass:               0   1   2    3
        for range +/-:      250 500 1000 2000  degrees/second
        '''
        gr_bytes = (0x00, 0x08, 0x10, 0x18)
        if gyro_range in range(len(gr_bytes)):
            try:
                # Sets fchoice = b11 which enables filter
                self._write(gr_bytes[gyro_range], 0x1B, self.mpu_addr)
            except OSError:
                raise MPUException(self._I2Cerror)
        else:
            raise ValueError('gyro_range can only be 0, 1, 2 or 3')

    # Accelerometer
    @property
    def accel(self):
        '''
        Acceleremoter object
        '''
        return self._accel

    def _accel_callback(self):
        '''
        Update accelerometer Vector3d object
        '''
        try:
            self._read(self.buf6, 0x3B, self.mpu_addr)
        except OSError:
            raise MPUException(self._I2Cerror)
        self._accel._ivector[0] = bytes_toint(self.buf6[0], self.buf6[1])
        self._accel._ivector[1] = bytes_toint(self.buf6[2], self.buf6[3])
        self._accel._ivector[2] = bytes_toint(self.buf6[4], self.buf6[5])
        scale = (16384, 8192, 4096, 2048)
        self._accel._vector[0] = self._accel._ivector[0] / \
            scale[self.accel_range]
        self._accel._vector[1] = self._accel._ivector[1] / \
            scale[self.accel_range]
        self._accel._vector[2] = self._accel._ivector[2] / \
            scale[self.accel_range]

    def get_accel_irq(self):
        '''
        For use in interrupt handlers. Sets self._accel._ivector[] to signed
        unscaled integer accelerometer values
        '''
        self._read(self.buf6, 0x3B, self.mpu_addr)
        self._accel._ivector[0] = bytes_toint(self.buf6[0], self.buf6[1])
        self._accel._ivector[1] = bytes_toint(self.buf6[2], self.buf6[3])
        self._accel._ivector[2] = bytes_toint(self.buf6[4], self.buf6[5])

    # Gyro
    @property
    def gyro(self):
        '''
        Gyroscope object
        '''
        return self._gyro

    def _gyro_callback(self):
        '''
        Update gyroscope Vector3d object
        '''
        try:
            self._read(self.buf6, 0x43, self.mpu_addr)
        except OSError:
            raise MPUException(self._I2Cerror)
        self._gyro._ivector[0] = bytes_toint(self.buf6[0], self.buf6[1])
        self._gyro._ivector[1] = bytes_toint(self.buf6[2], self.buf6[3])
        self._gyro._ivector[2] = bytes_toint(self.buf6[4], self.buf6[5])
        scale = (131, 65.5, 32.8, 16.4)
        self._gyro._vector[0] = self._gyro._ivector[0]/scale[self.gyro_range]
        self._gyro._vector[1] = self._gyro._ivector[1]/scale[self.gyro_range]
        self._gyro._vector[2] = self._gyro._ivector[2]/scale[self.gyro_range]

    def get_gyro_irq(self):
        '''
        For use in interrupt handlers. Sets self._gyro._ivector[] to signed
        unscaled integer gyro values. Error trapping disallowed.
        '''
        self._read(self.buf6, 0x43, self.mpu_addr)
        self._gyro._ivector[0] = bytes_toint(self.buf6[0], self.buf6[1])
        self._gyro._ivector[1] = bytes_toint(self.buf6[2], self.buf6[3])
        self._gyro._ivector[2] = bytes_toint(self.buf6[4], self.buf6[5])

    def is_shaking(self, threshold: float = None) -> bool:
        """
        @desc: Detect if the device is being shaken by monitoring acceleration changes
               Compares current acceleration with previous reading across all axes

        @args:
            - threshold: Optional custom threshold value in g (default: 1.5g)

        @returns:
            bool: True if shaking detected, False otherwise

        @example:
            >>> if imu.is_shaking(threshold=2.0):
            ...     print("Strong shake detected!")
        """
        if threshold is not None:
            self._shake_threshold = threshold

        # Get current acceleration values
        current_accel = self.accel.xyz
        
        # Calculate the acceleration change
        delta_x = abs(current_accel[0] - self._last_accel[0])
        delta_y = abs(current_accel[1] - self._last_accel[1])
        delta_z = abs(current_accel[2] - self._last_accel[2])
        
        # Update last acceleration values
        self._last_accel = current_accel
        
        # Check if acceleration change exceeds threshold on any axis
        return (delta_x > self._shake_threshold or 
                delta_y > self._shake_threshold or 
                delta_z > self._shake_threshold)

    def set_yaw_ratio(self, ratio: float = 0.55):
        self._yaw_ratio = ratio
    
    def calibrate_gyro(self, samples: int = 100):
        """
        @desc: Calibrate gyroscope to reduce drift
        @args: samples - Number of samples to use for calibration
        """
        print("Calibrating gyroscope - keep device still...")
        
        sum_gx = sum_gy = sum_gz = 0
        
        for _ in range(samples):
            gx = self.gyro.x
            gy = self.gyro.y
            gz = self.gyro.z
            sum_gx += gx
            sum_gy += gy
            sum_gz += gz
            time.sleep_ms(10)
        
        # Calculate offsets
        self._gyro.cal = [
            sum_gx / samples,
            sum_gy / samples,
            sum_gz / samples
        ]
        
        print("Gyroscope calibration complete")

    def update(self):
        """
        @desc: Update pitch, roll, and yaw calculations from sensor data
               Pitch and roll are calculated from accelerometer
               Yaw is calculated by integrating gyroscope data

        @example:
            >>> imu.update()
            >>> print(f"Orientation: Pitch={imu.pitchDeg}°, Roll={imu.rollDeg}°, Yaw={imu.yawDeg}°")
        """
        # Get accelerometer data
        ax = self.accel.x
        ay = self.accel.y
        az = self.accel.z
        
        # Get gyroscope data (degrees/second)
        gx = self.gyro.x
        gy = self.gyro.y
        gz = self.gyro.z
        
        # Get current time and calculate delta time
        current_time = time.ticks_ms()
        dt = (current_time - self._last_update) / 1000.0  # Convert to seconds
            
        try:
            accel_pitch = math.degrees(math.atan2(-ax, math.sqrt(ay * ay + az * az)))
            accel_roll = math.degrees(math.atan2(ay, az))
        except:
            accel_pitch = 0
            accel_roll = 0
        
        # Complementary filter for pitch and roll
        # Combine accelerometer and gyroscope data
        # Alpha determines how much we trust each sensor (0.96 = 96% gyro, 4% accel)
        alpha = 0.96
        
        if self.pitchDeg is None:
            self.pitchDeg = accel_pitch
        else:
            self.pitchDeg = alpha * (self.pitchDeg + gy * dt) + (1 - alpha) * accel_pitch
        
        if self.rollDeg is None:
            self.rollDeg = accel_roll
        else:
            self.rollDeg = alpha * (self.rollDeg + gx * dt) + (1 - alpha) * accel_roll
        
        # Yaw calculation with enhanced drift compensation
        if self._yawDeg is None:
            self._yawDeg = 0
        else:
            # Compensate for gimbal lock
            if abs(self.pitchDeg) > 85:
                # Adjust gyro_z based on roll angle when close to gimbal lock
                gz = gz * math.cos(math.radians(self.rollDeg))
            
            # Apply deadband to reduce drift when not moving
            if abs(gz) < 0.1:  # Increased threshold
                gz = 0
            
            # Calculate raw yaw change
            yaw_rate = gz
            raw_yaw = self._yawDeg + yaw_rate * dt
            
            self._yawDeg = raw_yaw
            
            # Normalize yaw to -180 to +180 degrees
            self._yawDeg = ((self._yawDeg + 180) % 360) - 180
        
        # Round values for display
        self.pitchDeg = round(self.pitchDeg, 1)
        self.rollDeg = round(self.rollDeg, 1)
        self.yawDeg = round(self._yawDeg * self._yaw_ratio, 1)
        
        # Update last update time
        self._last_update = current_time

    @property
    def orientation(self) -> tuple:
        """
        @desc: Get all orientation angles in one call

        @returns:
            tuple: (pitch, roll, yaw) in degrees

        @example:
            >>> pitch, roll, yaw = imu.orientation
            >>> print(f"Pitch: {pitch}°, Roll: {roll}°, Yaw: {yaw}°")
        """
        self.update()
        return (self.pitchDeg, self.rollDeg, self.yawDeg)
