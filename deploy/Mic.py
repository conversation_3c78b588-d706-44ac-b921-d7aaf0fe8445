from machine import Pin, ADC

class Mic:
    """
    @desc: Mic interface for BeE board
           Supports reading raw and percentage values, and threshold checks

    @example:
        >>> from BeeBrain import bee
        >>> mic = Mic(port=bee.PORT1)
        >>> if mic.threshold(50):
        ...     print("Noise detected!")
    """
    def __init__(self, port = None):
        """
        @desc: Initialize mic with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> mic = Mic(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__pin = ADC(Pin(port.use_pins(1)))

    def read_raw(self):
        """
        @desc: Read raw value from mic

        @returns:
            - int: Raw value (0-4095)

        @example:
            >>> raw = mic.read_raw()
            >>> print(f"Raw value: {raw}")
        """
        return self.__pin.read()

    def read(self) -> int:
        """
        @desc: Read percentage value from mic

        @returns:
            - int: Percentage value (0-100)

        @example:
            >>> percent = mic.read()
            >>> print(f"Percentage: {percent}%")
        """
        return int(self.read_raw() * 100.0 / 4095.0)

    def threshold(self, threshold: int) -> bool:
        """
        @desc: Check if mic value is above threshold

        @args:
            - threshold: Threshold value (0-100)

        @returns:
            - bool: True if value is above threshold, False otherwise

        @example:
            >>> if mic.threshold(50):
            ...     print("Noise detected!")
        """
        if self.read() > threshold:
            return True
        return False
