from machine import Pin, ADC

class GroveRotationSensor:
    """
    @desc: Interface for Grove Rotation Sensor
           Supports angle and percentage readings, and threshold checks

    @example:
        >>> from BeeBrain import bee
        >>> sensor = RotationSensor(port=bee.PORT1)
        >>> if sensor.is_rotated_past(50):
        ...     print("Rotated past 50%!")
    """
    def __init__(self, port):
        """
        @desc: Initialize rotation sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = RotationSensor(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__adc = ADC(Pin(port.use_pins(1)))
        self.__max_adc = 4095
        self.__full_angle = 300

    @property
    def read_angle_raw(self):
        """
        @desc: Read raw ADC value from sensor

        @returns:
            - int: Raw ADC value (0-4095)

        @example:
            >>> raw = sensor.read_angle_raw
            >>> print(f"Raw value: {raw}")
        """
        return self.__adc.read()

    @property
    def read_angle_degrees(self):
        """
        @desc: Read angle in degrees from sensor

        @returns:
            - float: Angle in degrees (0-300)

        @example:
            >>> angle = sensor.read_angle_degrees
            >>> print(f"Angle: {angle}°")
        """
        raw_value = self.read_angle_raw
        voltage = (raw_value * 3.3) / self.__max_adc
        degrees = (voltage * self.__full_angle) / 3.3
        return round(degrees, 1)

    @property
    def read_angle_percentage(self):
        """
        @desc: Read angle as percentage from sensor

        @returns:
            - int: Angle as percentage (0-100)

        @example:
            >>> percent = sensor.read_angle_percentage
            >>> print(f"Percentage: {percent}%")
        """
        raw_value = self.read_angle_raw
        return (raw_value * 100) // self.__max_adc

    def is_rotated_past(self, threshold_percent=50, unit="percentage"):
        """
        @desc: Check if sensor has been rotated past threshold

        @args:
            - threshold_percent: Optional threshold percentage (0-100)
            - unit: Optional unit of threshold ("percentage" or "degree")

        @returns:
            - bool: True if sensor has been rotated past threshold

        @example:
            >>> if sensor.is_rotated_past(70):
            ...     print("Rotated past 70%!")
        """
        if unit == "degree":
            threshold_percent = (threshold_percent / self.__full_angle) * 100
        threshold = (threshold_percent * self._max_adc) // 100
        return self.read_angle_raw > threshold

    def is_rotated_below(self, threshold_percent=50, unit="percentage"):
        """
        @desc: Check if sensor has been rotated below threshold

        @args:
            - threshold_percent: Optional threshold percentage (0-100)
            - unit: Optional unit of threshold ("percentage" or "degree")

        @returns:
            - bool: True if sensor has been rotated below threshold

        @example:
            >>> if sensor.is_rotated_below(30):
            ...     print("Rotated below 30%!")
        """
        if unit == "degree":
            threshold_percent = (threshold_percent / self.__full_angle) * 100
        threshold = (threshold_percent * self.__max_adc) // 100
        return self.read_angle_raw <= threshold