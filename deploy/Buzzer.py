from machine import Pin, PWM
from utime import sleep

class Buzzer:
    """
    @desc: Music/tone generator interface for BeE board using PWM
           Supports playing individual tones and sequences of notes

    @example:
        >>> from BeeBrain import bee
        >>> buzzer = Buzzer(bee.PORT1)
        >>> buzzer.play_tone(440)  # Play A4 note
        >>> buzzer.play_song("C4 E4 G4")  # Play C major chord

    @version: 1.2.0
    """

    # Musical note frequencies in Hz
    TONES = {
        "C4": 261,
        "C#4": 277,
        "D4": 293,
        "Eb4": 311,
        "E4": 329,
        "F4": 349,
        "F#4": 369,
        "G4": 391,
        "G#4": 415,
        "A4": 440,
        "Bb4": 466,
        "B4": 493,
        "C5": 523,
        "C#5": 554,
        "D5": 587,
        "Eb5": 622,
        "E5": 659,
        "F5": 698,
        "F#5": 740,
        "G5": 784,
        "G#5": 831,
        "A5": 880,
        "Bb5": 932,
        "B5": 988,
        "C6": 1046,
        "C#6": 1109,
        "D6": 1175,
        "Eb6": 1244,
        "E6": 1318,
        "F6": 1396,
        "F#6": 1480,
        "G6": 1568,
        "G#6": 1661,
        "A6": 1760,
        "Bb6": 1865,
        "B6": 1976,
        "C7": 2093,
    }

    def __init__(self, port = None, volume: int = 50, pin = None):
        """
        @desc: Initialize buzzer with specified pin and volume level

        @args:
            - pin (int): GPIO pin number for buzzer
            - volume (int): Initial volume level (0-100), defaults to 50

        @raises:
            - ValueError: If pin is invalid
            - ValueError: If volume is outside valid range

        @example:
            >>> buzzer = Buzzer(bee.PORT1)  # Default volume
            >>> buzzer = Buzzer(bee.PORT1, volume=75)  # Custom volume
        """
        self.__pin = None
        if port is not None:
            self.__pin = port.use_pins(1)
        if pin is not None:
            self.__pin = pin
        if not self.__pin:
            raise ValueError("Invalid port")
        self.__buzzer = PWM(Pin(self.__pin))
        self.__volume = 0
        self.__duty_cycle = 0
        self.set_volume(volume)
        self.be_quiet()

    def set_volume(self, volume: int) -> None:
        """
        @desc: Set buzzer volume level

        @args:
            - volume (int): Volume level (0-100)
                * 0: Silent
                * 100: Maximum volume

        @note: Volume is implemented using PWM duty cycle

        @example:
            >>> buzzer.set_volume(75)  # Set to 75% volume
        """
        self.__volume = volume if 0 < volume < 100 else 0 if volume < 0 else 100
        self.__duty_cycle = int(self.__volume / 100.0 * 6000)

    def play_tone(self, frequency: int) -> None:
        """
        @desc: Play a single tone at specified frequency

        @args:
            - frequency (int): Frequency in Hz to play

        @note: Use be_quiet() to stop the tone

        @example:
            >>> buzzer.play_tone(440)  # Play A4 note
            >>> sleep(1)
            >>> buzzer.be_quiet()
        """
        self.__buzzer.duty_u16(self.__duty_cycle)
        self.__buzzer.freq(frequency)

    def be_quiet(self) -> None:
        """
        @desc: Stop playing any current tone

        @example:
            >>> buzzer.be_quiet()  # Stop all sound
        """
        self.__buzzer.duty_u16(0)

    def play_song(self, mysong, origin_delay: float = 0.5) -> None:
        """
        @desc: Play a sequence of musical notes

        @args:
            - mysong: Either a space-separated string of notes or list of notes
                * Notes can be from TONES dictionary (e.g., "C4", "F#5")
                * "SIL" for silence
                * Note format: "note[:duration]" (e.g., "C4:1.0")
            - origin_delay (float): Default duration for each note in seconds, defaults to 0.5

        @note: 
            - Notes without explicit duration use origin_delay
            - Use "SIL" for rests in the melody
            - Automatically stops after playing all notes

        @example:
            >>> # Play a simple melody
            >>> buzzer.play_song("C4 E4 G4")
            >>> 
            >>> # Play with custom durations
            >>> buzzer.play_song("C4:1.0 E4:0.5 G4:2.0")
            >>> 
            >>> # Play with silence
            >>> buzzer.play_song("C4 SIL E4 SIL G4")
        """
        if type(mysong) == str:
            mysong = mysong.split(" ")
        for note_i in mysong:
            note = note_i if ":" not in note_i else note_i.split(':')[0]
            delay = origin_delay if ":" not in note_i else float(note_i.split(':')[1])
            if note == "SIL":
                self.be_quiet()
                sleep(delay)
            else:
                if note in self.TONES:
                    self.play_tone(self.TONES[note])
                    sleep(delay)
        self.be_quiet()

    def beep(self, duration: float = 0.1) -> None:
        """
        @desc: Play a short beep sound

        @args:
            - duration (float): Duration of beep in seconds, defaults to 0.1

        @example:
            >>> buzzer.beep()  # Short beep
            >>> buzzer.beep(0.5)  # Longer beep
        """
        self.play_tone(440)
        sleep(duration)
        self.be_quiet()
