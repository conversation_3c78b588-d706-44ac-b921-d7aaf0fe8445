# - STOP → PUT/DATA/END → RUN (without reset)
# - Map main.py -> /main_user.py; autorun /main_user.py sau boot
# - CHỈ can thiệp time.sleep/utime.sleep bằng "proxy" sys.modules['time'] (không gán đè hàm built-in)
# - Redirect builtins.print -> TX; throttle notify

try:
    import bluetooth as bt
except ImportError:
    import ubluetooth as bt

import binascii

from BeeBrain import bee
import time

from micropython import const
import _thread
import sys

# ===== Cấu hình =====
NAME = "BeE-board"
ADV_INTERVAL_US = 300_000       # 300 ms (µs)
THREAD_STACK = 16 * 1024        # stack cho thread user-code
TX_CHUNK = 80                   # notify tối đa ~80B
TX_YIELD_S = 0.001              # 1ms (tính theo giây) giữa các notify
AUTORUN = True                  # autorun sau boot nếu có file
AUTORUN_PATH = "/main_user.py"  # nơi lưu/chạy chương trình người dùng

# ===== UUID UART-like =====
UART_SERVICE = bt.UUID("6E400001-B5A3-F393-E0A9-E50E24DCCA9E")
UART_TX = (bt.UUID("6E400003-B5A3-F393-E0A9-E50E24DCCA9E"), bt.FLAG_NOTIFY)
UART_RX = (bt.UUID("6E400002-B5A3-F393-E0A9-E50E24DCCA9E"),
           bt.FLAG_WRITE | bt.FLAG_WRITE_NO_RESPONSE)
UART_SERVICE_TUPLE = (UART_SERVICE, (UART_TX, UART_RX))

# ===== IRQ codes =====
IRQ_CENTRAL_CONNECT = const(1)
IRQ_CENTRAL_DISCONNECT = const(2)
IRQ_GATTS_WRITE = const(3)


class BLESupervisor:
    """
    @desc: BLE interface for BeE board, providing UART-like communication
           and file transfer capabilities

    @requires: 
        - bluetooth
        - BeeBrain

    @example:
        >>> ble = BLESupervisor()
        >>> ble.advertise()
    """
    VERSION = b"1.9"

    def __init__(self, name=NAME, adv_interval_us=ADV_INTERVAL_US):
        try:
            _thread.stack_size(THREAD_STACK)
        except:
            pass

        self.ble = bt.BLE()
        self.ble.active(True)
        self.ble.irq(self._irq)
        ((self.tx_handle, self.rx_handle),) = self.ble.gatts_register_services(
            (UART_SERVICE_TUPLE,))
        try:
            self.ble.gatts_set_buffer(self.rx_handle, 512, True)
        except:
            pass
        self.ble.gatts_write(self.tx_handle, b"")

        # trạng thái giao thức
        self.conn = None
        self.buf = bytearray()
        self.state = "idle"
        self.target = None
        self.left = 0

        # runtime
        self.running = False
        self.stop_flag = False
        self._patch_tokens = []  # để unpatch (sys.modules entries)

        self._adv_with_unique_name()  # name, adv_interval_us)

        # autorun sau boot nếu có file
        if AUTORUN:
            try:
                import uos
                uos.stat(AUTORUN_PATH)          # có file thì chạy
                self._sleep_s(0.15)             # đợi BLE ổn
                try:
                    _thread.start_new_thread(
                        self._run_file_thread, (AUTORUN_PATH,))
                except Exception as e:
                    self._send_err("THREAD", e)
            except:
                pass

    # ---------- sleep tiện ích (seconds) ----------
    def _sleep_s(self, s):
        try:
            import time as _t
            if hasattr(_t, "sleep"):
                _t.sleep(s)
                return
        except:
            pass
        try:
            import utime as _ut
            if hasattr(_ut, "sleep"):
                _ut.sleep(s)
                return
        except:
            pass

    # ---------- BLE ----------
    def _advertise(self, name, interval_us):
        FLAGS = b"\x02\x01\x06"
        adv = bytearray(FLAGS) + bytes((len(name)+1, 0x09)) + name.encode()
        try:
            self.ble.config(gap_name=name)
        except:
            pass
        self.ble.gap_advertise(interval_us, adv_data=adv, connectable=True)

    def _adv_with_unique_name(self):
        mac = self.ble.config('mac')  # 6 bytes
        suf = binascii.hexlify(mac[1]).decode(
        )[-6:].upper()  # 3 byte cuối = 6 hex
        name = 'BeE-' + suf          # ví dụ: BeE-12AB3C
        FLAGS = b'\x02\x01\x06'

        # UUID 128-bit để browser lọc -> adv_data
        svc128 = bytes(UART_SERVICE)
        adv = bytearray(FLAGS) + bytes((len(svc128)+1, 0x07)) + svc128

        # Tên đầy đủ -> scan response (tránh tràn 31B)
        scan_resp = bytes((len(name)+1, 0x09)) + name.encode()

        try:
            self.ble.config(gap_name=name)
        except:
            pass
        bee.oled.clear()
        bee.oled.text(f"Please connect to", 10, 24, 1)
        bee.oled.text(name, 24, 36, 1)
        bee.oled.show()
        time.sleep(1)
        self.ble.gap_advertise(300_000, adv_data=adv,
                               resp_data=scan_resp, connectable=True)

    def _irq(self, event, data):
        if event == IRQ_CENTRAL_CONNECT:
            self.conn, _, _ = data
            try:
                self.ble.gap_update_conn_params(self.conn, 24, 40, 0, 400)
            except:
                pass
        elif event == IRQ_CENTRAL_DISCONNECT:
            self.conn = None
            self._adv_with_unique_name()  # NAME, ADV_INTERVAL_US)
        elif event == IRQ_GATTS_WRITE:
            conn_handle, value_handle = data
            if value_handle == self.rx_handle:
                chunk = self.ble.gatts_read(value_handle)
                self._process(chunk)

    # ---------- Notify ----------
    def _notify(self, msg: bytes):
        if self.conn is None:
            return
        try:
            self.ble.gatts_write(self.tx_handle, msg)
            self.ble.gatts_notify(self.conn, self.tx_handle)
        except:
            pass

    def _notify_text(self, s):
        try:
            b = s.encode() if isinstance(s, str) else s
            for raw in b.split(b"\n"):
                line = raw
                for i in range(0, len(line), TX_CHUNK):
                    self._notify(line[i:i+TX_CHUNK])
                    self._sleep_s(TX_YIELD_S)
                self._notify(b"\n")
                self._sleep_s(TX_YIELD_S)
        except:
            pass

    def _send_err(self, tag, e):
        try:
            msg = "ERR {}: {}".format(tag, repr(e))[:180]
            self._notify_text(msg)
        except:
            self._notify(b"ERR\n")

    # ---------- Helpers ----------
    def _norm_path(self, p: str) -> str:
        return p if p.startswith("/") else "/" + p

    def _map_main(self, p: str) -> str:
        p = p.strip()
        if p.endswith("main.py"):
            self._notify_text("INFO MAIN->{}".format(AUTORUN_PATH))
            return AUTORUN_PATH
        return p

    def _stop_soft(self, timeout_ms=2000):
        if not self.running:
            return True
        self.stop_flag = True
        t0 = self._now_s()
        while self.running and (self._now_s() - t0) < (timeout_ms/1000.0):
            self._sleep_s(0.05)  # lát 50ms
        return not self.running

    def _now_s(self):
        try:
            import time as _t
            return _t.time()
        except:
            try:
                import utime as _ut
                return _ut.time()
            except:
                return 0.0

    # ---------- Proxy 'time' trong sys.modules (không gán đè hàm built-in) ----------
    def _install_time_proxy(self):
        # Lấy bản gốc (nếu có)
        try:
            import time as _t
        except:
            _t = None
        try:
            import utime as _ut
        except:
            _ut = None

        def _check():
            if self.stop_flag:
                raise SystemExit

        # Tạo proxy có .sleep() cắt lát 50ms, các attr khác passthrough
        class _TimeProxy:
            def __init__(self, t, ut, check):
                self._t = t
                self._ut = ut
                self._check = check

            def sleep(self, sec):
                sec = float(sec or 0)
                if sec <= 0:
                    self._check()
                    return
                step = 0.05
                left = sec
                while left > 0:
                    self._check()
                    s = step if left >= step else left
                    # gọi sleep thật nếu có; ưu tiên time.sleep
                    if self._t and hasattr(self._t, "sleep"):
                        self._t.sleep(s)
                    elif self._ut and hasattr(self._ut, "sleep"):
                        self._ut.sleep(s)
                    else:
                        # không có gì để ngủ: bỏ qua nhưng vẫn check
                        pass
                    left -= s
                self._check()

            def __getattr__(self, name):
                # passthrough phần còn lại
                if self._t and hasattr(self._t, name):
                    return getattr(self._t, name)
                if self._ut and hasattr(self._ut, name):
                    return getattr(self._ut, name)
                raise AttributeError(name)

        old = sys.modules.get("time", None)
        sys.modules["time"] = _TimeProxy(_t, _ut, _check)
        self._patch_tokens.append(("sys_modules", "time", old))

    def _uninstall_time_proxy(self):
        # Khôi phục sys.modules['time']
        for kind, name, old in list(self._patch_tokens):
            if kind != "sys_modules":
                continue
            try:
                if old is None:
                    # xoá override nếu trước đó không có 'time'
                    if name in sys.modules:
                        del sys.modules[name]
                else:
                    sys.modules[name] = old
            except:
                pass
            # bỏ token
            self._patch_tokens.remove((kind, name, old))

    # ---------- Chạy user-code trong thread ----------
    def _run_file_thread(self, path):
        self.running = True
        self.stop_flag = False

        # Lắp proxy 'time' (import time -> dùng sleep đã cắt lát)
        self._install_time_proxy()

        # redirect builtins.print -> TX
        try:
            import builtins as _bi
        except ImportError:
            import __builtins__ as _bi
        old_print = _bi.print

        def tx_print(*args, **kwargs):
            sep = kwargs.get("sep", " ")
            end = kwargs.get("end", "\n")
            try:
                self._notify_text(sep.join(str(a) for a in args) + end)
            except:
                pass
            # echo ra USB REPL? mở 2 dòng dưới:
            # try: old_print(*args, **kwargs)
            # except: pass

        _bi.print = tx_print

        try:
            self._notify_text("OK RUNNING {}".format(path))
            g = {"__name__": "__main__",
                 "should_stop": (lambda: self.stop_flag)}
            with open(path, "r") as f:
                code = f.read()
            exec(code, g)
            self._notify(b"OK RUN\n")
        except SystemExit:
            self._notify(b"OK STOP\n")
        except Exception as e:
            self._send_err("RUN", e)
        finally:
            _bi.print = old_print
            self._uninstall_time_proxy()
            self.running = False
            self.stop_flag = False

    # ---------- Giao thức ----------
    def _process(self, chunk: bytes):
        self.buf += chunk

        while b"\n" in self.buf and self.state in ("idle", "data_wait"):
            line, _, rest = self.buf.partition(b"\n")
            self.buf = rest

            if self.state == "idle":
                if line.startswith(b"HELLO"):
                    self._notify(b"OK " + self.VERSION +
                                 (b" RUN\n" if self.running else b" IDLE\n"))
                elif line == b"STATUS":
                    self._notify(
                        b"STATE RUNNING\n" if self.running else b"STATE IDLE\n")
                elif line == b"PING":
                    self._notify(b"PONG\n")
                elif line == b"STOP":
                    if self.running:
                        self._notify(b"OK STOPPING\n")
                        ok = self._stop_soft()
                        self._notify(b"OK STOP\n" if ok else b"ERR BUSY\n")
                    else:
                        self._notify(b"OK IDLE\n")
                elif line.startswith(b"PUT "):
                    try:
                        _, path, size, _crc = line.decode().split()
                        path = self._map_main(self._norm_path(path))
                        self.target = path
                        self.left = int(size)
                        self.f = open(self.target, "wb")
                        self.state = "data_wait"
                        self._notify(b"OK PUT\n")
                    except Exception as e:
                        self._send_err("PUT", e)
                elif line.startswith(b"RUN "):
                    try:
                        _, path = line.decode().split()
                        path = self._map_main(self._norm_path(path))
                        if self.running and not self._stop_soft():
                            self._notify(b"ERR BUSY\n")
                        else:
                            try:
                                _thread.start_new_thread(
                                    self._run_file_thread, (path,))
                            except Exception as e:
                                self._send_err("THREAD", e)
                    except Exception as e:
                        self._send_err("RUN", e)
                elif line == b"END":
                    self._notify(b"ERR NO PUT\n")

            elif self.state == "data_wait":
                if line.startswith(b"DATA "):
                    try:
                        _, l = line.decode().split()
                        self.pending = int(l)
                        self.state = "data_payload"
                    except Exception as e:
                        self._send_err("DATA", e)
                elif line == b"END":
                    try:
                        self.f.close()
                        self.state = "idle"
                        self._notify(b"OK STORED\n")
                    except Exception as e:
                        self._send_err("END", e)
                else:
                    pass

        # payload của DATA <len>
        if self.state == "data_payload" and len(self.buf) >= getattr(self, "pending", 0):
            self.f.write(self.buf[:self.pending])
            self.left -= self.pending
            self.buf = self.buf[self.pending:]
            self._notify(b"OK CHUNK\n")
            self.state = "data_wait"
            if self.left <= 0:
                self.f.close()
                self.state = "idle"
                self._notify(b"OK STORED\n")


# khởi chạy supervisor ngay khi import
supervisor = BLESupervisor()
print("BeE BLE ready")
