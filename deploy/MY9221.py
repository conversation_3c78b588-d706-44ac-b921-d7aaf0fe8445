
from time import sleep_ms
from machine import Pin

class MY9221:
    """
    @desc: MY9221 LED driver interface for BeE board
           Supports controlling 10 LEDs with brightness control

    @example:
        >>> from MY9221 import MY9221
        >>> from BeeBrain import bee
        >>> led = MY9221(port=bee.PORT1)
        >>> led.level(5)  # Set all LEDs to level 5
        >>> led.bits(0b1010101010)  # Set LEDs based on bits
    """
    def __init__(self, port = None, reverse = False):
        """
        @desc: Initialize MY9221 LED driver with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - reverse: Optional reverse order of LEDs (default: False)

        @example:
            >>> led = MY9221(port=bee.PORT1)  # Default order
            >>> led = MY9221(port=bee.PORT2, reverse=True)  # Reverse order
        """
        if port is None:
            raise ValueError("Invalid port")
        self._d = Pin(port.use_pins(1))
        self._c = Pin(port.use_pins(2))
        self._r = reverse
        self._d.init(Pin.OUT, value=0)
        self._c.init(Pin.OUT, value=0)

    def _latch(self):
        self._d(0)
        sleep_ms(1)
        for i in range(4):
            self._d(1)
            self._d(0)
        sleep_ms(1)

    def _write16(self, data):
        for i in range(15,-1,-1):
            self._d((data >> i) & 1)
            state = self._c()
            self._c(not state)

    def _begin(self):
        self._write16(0) # command: 8bit mode

    def _end(self):
        self._write16(0)
        self._write16(0)
        self._latch()

    def reverse(self, val=None):
        """
        @desc: Get or set LED order reverse state

        @args:
            - val: Optional boolean value to set reverse state (None for get)

        @returns:
            - Current reverse state if val is None
            - None if setting new reverse state

        @example:
            >>> led.reverse(True)  # Set reverse order
            >>> is_reverse = led.reverse()  # Get current state
        """
        if val is None:
            return self._r
        self._r = val

    def level(self, val, brightness=255):
        """
        @desc: Set all LEDs to specified level

        @args:
            - val: Level to set (0-9)
            - brightness: Optional brightness value (0-255, default: 255)

        @example:
            >>> led.level(5)  # Set all LEDs to level 5
            >>> led.level(3, 128)  # Set all LEDs to level 3 with half brightness
        """
        self._begin()
        for i in range(9,-1,-1) if self._r else range(10):
            self._write16(brightness if val > i else 0)
        self._end()

    def bits(self, val, brightness=255):
        """
        @desc: Set LEDs based on bits

        @args:
            - val: Bitmask to set LEDs (0-0x3FF)
            - brightness: Optional brightness value (0-255, default: 255)

        @example:
            >>> led.bits(0b1010101010)  # Set odd LEDs
            >>> led.bits(0x2AA, 128)  # Set even LEDs with half brightness
        """
        val &= 0x3FF
        self._begin()
        for i in range(9,-1,-1) if self._r else range(10):
            self._write16(brightness if (val >> i) & 1 else 0)
        self._end()

    def bytes(self, buf):
        """
        @desc: Set LEDs based on bytes

        @args:
            - buf: Bytearray containing LED values (10 bytes)

        @example:
            >>> led.bytes(bytearray([255, 128, 64, 32, 16, 8, 4, 2, 1, 0]))  # Set LEDs with varying brightness
        """
        self._begin()
        for i in range(9,-1,-1) if self._r else range(10):
            self._write16(buf[i])
        self._end()