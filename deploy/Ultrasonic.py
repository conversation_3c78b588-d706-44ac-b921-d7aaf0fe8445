import time
from machine import Pin, time_pulse_us

class GroveUltrasonic:
    """
    @desc: Interface for Grove Ultrasonic Distance Sensor
           Supports distance measurements in millimeters and centimeters

    @example:
        >>> from BeeBrain import bee
        >>> sensor = Ultrasonic(port=bee.PORT1)
        >>> distance = sensor.distance_cm
        >>> print(f"Distance: {distance:.1f}cm")

    @version: 1.0.0
    """

    def __init__(self, port = None):
        """
        @desc: Initialize ultrasonic sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = Ultrasonic(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__sig = Pin(port.use_pins(1), Pin.OUT)

    @property
    def distance_cm(self) -> float:
        """
        @desc: Measure distance using ultrasonic sensor
               Sends trigger pulse and measures echo time
               Converts time to distance using speed of sound

        @returns:
            float: Distance in centimeters
                   -1.0 if measurement is out of range or invalid

        @note: Temperature affects accuracy due to speed of sound variation
               Calibrated for room temperature (20°C)

        @example:
            >>> distance = sensor.distance_cm
            >>> if distance != -1.0:
            ...     print(f"Distance: {distance:.1f}cm")
        """
        self.__sig.init(Pin.OUT)
        self.__sig.value(0)
        time.sleep_us(2)
        self.__sig.value(1)
        time.sleep_us(10)
        self.__sig.value(0)

        self.__sig.init(Pin.IN)
        try:
            duration = time_pulse_us(self.__sig, 1, 30000)
            distance = (duration * 0.0343) / 2
            return distance
        except OSError as ex:
            return -1

    @property
    def distance_mm(self) -> int:
        """
        @desc: Measure distance in millimeters
               Wrapper around distance_cm() with unit conversion

        @returns:
            int: Distance in millimeters
                 -1 if measurement is out of range or invalid

        @example:
            >>> distance = sensor.distance_mm
            >>> if distance != -1:
            ...     print(f"Distance: {distance}mm")
        """
        return int(self.distance_cm * 10)

class HCSR04:
    """
    @desc: Interface for HC-SR04 ultrasonic distance sensor
           Provides accurate distance measurements in millimeters
           Supports range detection from 20mm to 4000mm

    @requires:
        - machine.Pin
        - machine.time_pulse_us
        - time.sleep_us

    @example:
        >>> sensor = BeeUltrasonic(PORT1)
        >>> distance = sensor.distance_mm()
        >>> print(f"Distance: {distance}mm")

    @version: 1.0.0
    """

    # Constants for distance measurement
    __MAX_DETECT = 4000  # Maximum detection range in mm
    __MIN_DETECT = 20    # Minimum detection range in mm
    __TIMEOUT_US = 30000 # Maximum timeout for echo pulse (30ms)
    __SPEED_SOUND = 340  # Speed of sound in m/s at room temperature

    def __init__(self, port) -> None:
        """
        @desc: Initialize ultrasonic sensor with trigger and echo pins

        @args:
            - port: BeE board port object (PORT1-PORT6)
                   Pin 1 is used for trigger
                   Pin 2 is used for echo

        @raises:
            - ValueError: If port pins are invalid

        @example:
            >>> sensor = BeeUltrasonic(PORT1)
        """
        self.__triggerPin = Pin(port.use_pins(1), Pin.OUT)
        self.__echoPin = Pin(port.use_pins(2), Pin.IN, pull=None)
        # Initialize trigger pin to low
        self.__triggerPin.value(0)
        # Set timeout for echo pulse (500µs * 2 * 30)
        self.echo_timeout_us = __TIMEOUT_US

    def distance_mm(self) -> int:
        """
        @desc: Measure distance using ultrasonic sensor
               Sends trigger pulse and measures echo time
               Converts time to distance using speed of sound

        @returns:
            int: Distance in millimeters
                 -1 if measurement is out of range or invalid

        @note: Temperature affects accuracy due to speed of sound variation
               Calibrated for room temperature (20°C)

        @example:
            >>> distance = sensor.distance_mm()
            >>> if distance != -1:
            ...     print(f"Object detected at {distance}mm")
            ... else:
            ...     print("No object detected in range")
        """
        # Reset trigger
        self.__triggerPin.value(0)
        time.sleep_us(5)
        
        # Send trigger pulse
        self.__triggerPin.value(1)
        time.sleep_us(10)
        self.__triggerPin.value(0)

        # Measure echo pulse duration
        pulse_time = time_pulse_us(
            self.__echoPin, 1, self.echo_timeout_us)

        # Convert pulse time to distance
        # Formula: distance = (time * speed_of_sound) / 2
        # 582 is derived from: (speed_of_sound * 100) / (2 * 1000000)
        # where 100 is for cm to mm conversion
        distance = int(pulse_time * 100 // 582)

        # Check if distance is within valid range
        if distance > __MAX_DETECT or distance < __MIN_DETECT:
            return -1
            
        return distance

    def distance_cm(self) -> float:
        """
        @desc: Get distance measurement in centimeters
               Wrapper around distance_mm() with unit conversion

        @returns:
            float: Distance in centimeters
                  -1.0 if measurement is out of range or invalid

        @example:
            >>> distance = sensor.distance_cm()
            >>> print(f"Distance: {distance:.1f}cm")
        """
        mm_distance = self.distance_mm()
        if mm_distance == -1:
            return -1.0
        return mm_distance / 10.0

    def is_object_present(self, threshold_mm: int = 200) -> bool:
        """
        @desc: Check if an object is present within specified distance

        @args:
            - threshold_mm: Detection threshold in millimeters (default: 200mm)

        @returns:
            bool: True if object detected within threshold, False otherwise

        @example:
            >>> if sensor.is_object_present(300):
            ...     print("Object detected within 30cm")
        """
        distance = self.distance_mm()
        return distance != -1 and distance <= threshold_mm
