import time

from machine import Pin

class PirSensor:
    """
    @desc: Interface for PIR sensor
           Supports motion detection and wait-for-motion functionality

    @example:
        >>> from BeeBrain import bee
        >>> sensor = PirSensor(port=bee.PORT1)
        >>> if sensor.is_motion_detected():
        ...     print("Motion detected!")
        >>> sensor.wait_for_motion()  # Block until motion detected
    """
    
    def __init__(self, port = None):
        """
        @desc: Initialize PIR sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = PirSensor(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__pin = Pin(port.use_pins(1), Pin.IN)

    @property
    def is_motion_detected(self):
        """
        @desc: Check if motion is currently detected

        @returns:
            - bool: True if motion is detected, False otherwise

        @example:
            >>> if sensor.is_motion_detected():
            ...     print("Motion detected!")
        """
        return self.__pin.value() == 1

    def wait_for_motion(self, timeout=10):
        """
        @desc: Wait for motion to be detected

        @args:
            - timeout (int): Maximum time to wait in seconds, defaults to 10

        @returns:
            - bool: True if motion detected within timeout, False otherwise

        @example:
            >>> sensor.wait_for_motion()  # Wait indefinitely
            >>> sensor.wait_for_motion(5)  # Wait for 5 seconds max
        """
        start = time.ticks_ms()
        while time.ticks_diff(time.ticks_ms(), start) < timeout * 1000:
            if self.is_motion_detected:
                return True
            time.sleep_ms(100)
        return False
