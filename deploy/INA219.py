from machine import I2C, Pin

class INA219:
    """
    @desc: Interface for INA219 voltage sensor
           Supports reading raw voltage and percentage

    @example:
        >>> from BeeBrain import bee
        >>> sensor = INA219(port=bee.PORT1)
        >>> voltage = sensor.read_raw()
        >>> percent = sensor.read_percentage()
    """

    ADDR = 0x40
    MAX_VOLTAGE = 8.4
    MIN_VOLTAGE = 7.2

    def __init__(self, port=None):
        """
        @desc: Initialize INA219 sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = INA219(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)), timeout=100000)
        self.i2c.writeto_mem(self.ADDR, 0x00, b'\x39\x9F')

    def read_raw(self):
        """
        @desc: Read raw voltage from sensor

        @returns:
            - float: Raw voltage value

        @example:
            >>> voltage = sensor.read_raw()
            >>> print(f"Voltage: {voltage}V")
        """
        data = self.i2c.readfrom_mem(self.ADDR, 0x02, 2)
        raw = (data[0] << 8) | data[1]
        raw >>= 3
        voltage = raw * 0.004
        return voltage

    def read_percentage(self):
        """
        @desc: Read voltage as percentage

        @returns:
            - int: Voltage as percentage (0-100)

        @example:
            >>> percent = sensor.read_percentage()
            >>> print(f"Percentage: {percent}%")
        """
        percent = int((self.read_raw() - self.MIN_VOLTAGE) / (self.MAX_VOLTAGE - self.MIN_VOLTAGE) * 100)
        return min(max(percent, 0), 100)
