from machine import Pin
import neopixel
import time
import math

class Digits:

    HEIGHT = 5
    WIDTH = 5
    LENGTH = (HEIGHT * WIDTH)

    def __init__(self):
        pass

    def get_digit(self, position, data):
        start_pos = position * self.LENGTH
        end_pos = start_pos + self.LENGTH
        buffer = data[start_pos:end_pos]
        return (buffer)

    digits_left = [

        1, 1, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

        0, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 0, 0, 0,

        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        1, 0, 0, 0, 0,
        1, 1, 1, 0, 0,

        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

        1, 0, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,

        1, 1, 1, 0, 0,
        1, 0, 0, 0, 0,
        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

        1, 1, 1, 0, 0,
        1, 0, 0, 0, 0,
        1, 1, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,

        1, 1, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

        1, 1, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 0, 0,

    ]

    digits_right = [

        0, 0, 1, 1, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,

        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,

        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 1,

        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 1, 1, 1,

        0, 0, 1, 0, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 0, 0, 1,

        0, 0, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 1, 1, 1,

        0, 0, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,

        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 0, 0, 1,
        0, 0, 0, 0, 1,
        0, 0, 0, 0, 1,

        0, 0, 1, 1, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,

        0, 0, 1, 1, 1,
        0, 0, 1, 0, 1,
        0, 0, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 1, 1, 1,

    ]

    digits_center = [

        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,

        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,

        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 1, 0,

        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,

        0, 1, 0, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,

        0, 1, 1, 1, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,

        0, 1, 1, 1, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,

        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 1, 0,

        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,

        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,

    ]


class Font:

    def __init__(self):
        pass

    def get_letter(self, asci):
        # capture characters beyound 127
        if asci == 176:
            return self.celsius
        if asci == 248:
            return self.celsius
        # capture codes out of range
        if asci < 32:
            return self.empty
        if asci > 127:
            return self.empty
        # return valid character in range
        return self.data[(25*(asci-32)):(25*(asci-32+1))]

    empty = [
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0
    ]

    celsius = [
        # 176
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0
    ]

    data = [
        # 32 SPACE
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 33 !
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        # 34 "
        0, 1, 1, 0, 1,
        1, 1, 0, 1, 1,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 35 #
        0, 1, 0, 1, 0,
        1, 1, 1, 1, 1,
        0, 1, 0, 1, 0,
        1, 1, 1, 1, 1,
        0, 1, 0, 1, 0,
        # 36 $
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 37 %
        1, 1, 0, 1, 1,
        1, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 1,
        1, 1, 0, 1, 1,
        # 38 &
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 39 '
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 40 (
        0, 0, 1, 1, 0,
        0, 1, 1, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 0, 0,
        0, 0, 1, 1, 0,
        # 41 )
        0, 1, 1, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 1, 0,
        0, 0, 1, 1, 0,
        0, 1, 1, 0, 0,
        # 42 *
        1, 0, 1, 0, 1,
        0, 1, 1, 1, 0,
        1, 1, 1, 1, 1,
        0, 1, 1, 1, 0,
        1, 0, 1, 0, 1,
        # 43 +
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        # 44 ,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        0, 1, 1, 0, 0,
        # 45 -
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 1, 1, 1, 1,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 46 .
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 1, 1, 0, 0,
        0, 1, 1, 0, 0,
        # 47 /
        0, 0, 0, 1, 1,
        0, 0, 1, 1, 1,
        0, 1, 1, 1, 0,
        1, 1, 1, 0, 0,
        1, 1, 0, 0, 0,
        # 48 0
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 1, 0, 1, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 49 1
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        # 50 2
        1, 1, 1, 1, 0,
        1, 0, 0, 1, 1,
        0, 0, 1, 1, 1,
        0, 1, 1, 0, 0,
        1, 1, 1, 1, 1,
        # 51 3
        1, 1, 1, 1, 0,
        1, 0, 0, 1, 1,
        0, 0, 1, 1, 0,
        1, 0, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 52 4
        0, 0, 1, 1, 0,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 0,
        1, 1, 1, 1, 1,
        0, 0, 0, 1, 0,
        # 53 5
        1, 1, 1, 1, 1,
        1, 0, 0, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 54 6
        0, 1, 1, 1, 0,
        1, 1, 0, 0, 0,
        1, 1, 1, 1, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 55 7
        1, 1, 1, 1, 1,
        1, 0, 0, 0, 1,
        0, 0, 0, 1, 1,
        0, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        # 56 8
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 57 9
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 1, 1, 1, 1,
        0, 0, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 58 :
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        # 59 ;
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        0, 1, 1, 0, 0,
        # 60 <
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 0,
        1, 1, 0, 0, 0,
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        # 61 =
        0, 0, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 1, 1, 1, 0,
        # 62 >
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 1, 1,
        0, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        # 63 ?
        1, 1, 1, 1, 0,
        1, 0, 0, 1, 1,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 1, 1, 0,
        # 64 @
        0, 1, 1, 1, 0,
        1, 0, 1, 1, 1,
        1, 0, 1, 1, 1,
        1, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        # 65 A
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        1, 1, 1, 1, 1,
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        # 66 B
        1, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        1, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 67 C
        0, 0, 1, 1, 1,
        0, 1, 1, 0, 1,
        1, 1, 0, 0, 0,
        0, 1, 1, 0, 1,
        0, 0, 1, 1, 1,
        # 68 D
        1, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        0, 1, 0, 1, 1,
        0, 1, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 69 E
        1, 1, 1, 1, 1,
        0, 1, 0, 0, 1,
        1, 1, 1, 0, 0,
        0, 1, 0, 0, 1,
        1, 1, 1, 1, 1,
        # 70 F
        1, 1, 1, 1, 1,
        0, 1, 0, 0, 1,
        1, 1, 1, 0, 0,
        0, 1, 0, 0, 0,
        1, 1, 1, 0, 0,
        # 71 G
        0, 1, 1, 1, 0,
        1, 1, 0, 0, 0,
        1, 0, 0, 1, 1,
        1, 1, 0, 0, 1,
        0, 1, 1, 1, 1,
        # 72 H
        1, 1, 0, 1, 1,
        0, 1, 0, 0, 1,
        0, 1, 1, 1, 1,
        0, 1, 0, 0, 1,
        1, 1, 0, 1, 1,
        # 73 I
        1, 1, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        # 74 J
        1, 1, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 0, 1, 0, 0,
        1, 1, 1, 0, 0,
        # 75 K
        1, 0, 0, 1, 1,
        1, 0, 1, 1, 0,
        1, 1, 1, 0, 0,
        1, 0, 1, 1, 0,
        1, 0, 0, 1, 1,
        # 76 L
        1, 1, 1, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 0, 0, 1,
        1, 1, 1, 1, 1,
        # 77 M
        1, 1, 0, 1, 1,
        1, 1, 1, 1, 1,
        1, 0, 1, 0, 1,
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        # 78 N
        1, 1, 0, 0, 1,
        1, 1, 1, 0, 1,
        1, 0, 1, 0, 1,
        1, 0, 1, 1, 1,
        1, 0, 0, 1, 1,
        # 79 O
        0, 1, 1, 1, 0,
        1, 1, 1, 1, 1,
        1, 0, 0, 0, 1,
        1, 1, 1, 1, 1,
        0, 1, 1, 1, 0,
        # 80 P
        1, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 1, 0, 0, 0,
        1, 1, 1, 0, 0,
        # 81 Q
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 1,
        # 82 R
        1, 1, 1, 1, 1,
        0, 1, 1, 0, 1,
        1, 1, 1, 1, 1,
        0, 1, 0, 1, 0,
        1, 1, 0, 1, 1,
        # 83 S
        0, 1, 1, 1, 1,
        1, 1, 0, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 84 T
        1, 1, 1, 1, 1,
        1, 0, 1, 0, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        # 85 U
        1, 0, 0, 0, 1,
        1, 0, 0, 0, 1,
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 86 V
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        # 87 W
        1, 0, 0, 0, 1,
        1, 0, 1, 0, 1,
        1, 0, 1, 0, 1,
        1, 1, 1, 1, 1,
        0, 1, 0, 1, 0,
        # 88 X
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 0, 0, 0, 1,
        # 89 Y
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        # 90 Z
        1, 1, 1, 1, 1,
        1, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 1,
        1, 1, 1, 1, 1,
        # 91 [
        0, 1, 1, 1, 0,
        0, 1, 1, 0, 0,
        0, 1, 1, 0, 0,
        0, 1, 1, 0, 0,
        0, 1, 1, 1, 0,
        # 92 \
        1, 1, 0, 0, 0,
        1, 1, 1, 0, 0,
        0, 1, 1, 1, 0,
        0, 0, 1, 1, 1,
        0, 0, 0, 1, 1,
        # 93 ]
        0, 1, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 1, 1, 1, 0,
        # 94 ^
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 95 _
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        1, 1, 1, 1, 1,
        1, 1, 1, 1, 1,
        # 96 '
        0, 1, 1, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        # 97 a
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 1,
        1, 1, 1, 1, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 98 b
        1, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 99 c
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 0, 0, 0, 0,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        # 100 d
        0, 0, 0, 1, 1,
        0, 0, 0, 1, 0,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 0,
        0, 1, 1, 1, 1,
        # 101 e
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 1, 1, 1, 1,
        1, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        # 102 f
        0, 0, 1, 1, 1,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        # 103 g
        0, 0, 1, 1, 1,
        0, 1, 1, 0, 1,
        0, 0, 1, 1, 1,
        1, 0, 0, 0, 1,
        1, 1, 1, 1, 1,
        # 104 h
        1, 1, 0, 0, 0,
        0, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 0,
        1, 1, 0, 1, 1,
        # 105 i
        0, 0, 1, 0, 0,
        0, 0, 0, 0, 0,
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        # 106 j
        0, 0, 0, 0, 1,
        0, 0, 0, 0, 0,
        0, 0, 0, 1, 1,
        1, 0, 0, 0, 1,
        1, 1, 1, 1, 1,
        # 107 k
        1, 1, 0, 0, 0,
        0, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 1, 0, 1, 1,
        1, 1, 0, 0, 1,
        # 108 l
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        # 10 m
        1, 0, 0, 0, 0,
        1, 1, 1, 1, 1,
        1, 0, 1, 0, 1,
        1, 0, 1, 0, 1,
        1, 0, 1, 0, 1,
        # 110 n
        1, 0, 0, 0, 0,
        1, 1, 1, 1, 0,
        1, 0, 0, 1, 0,
        1, 0, 0, 1, 0,
        1, 0, 0, 1, 1,
        # 111 o
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        # 112 p
        1, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        1, 1, 1, 1, 0,
        1, 0, 0, 0, 0,
        1, 1, 0, 0, 0,
        # 113 q
        0, 1, 1, 1, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 1,
        0, 0, 0, 0, 1,
        0, 0, 0, 1, 1,
        # 114 r
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 1,
        0, 1, 0, 0, 1,
        0, 1, 0, 0, 0,
        1, 1, 1, 0, 0,
        # 115 s
        0, 1, 0, 0, 0,
        1, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 1,
        1, 1, 1, 1, 0,
        # 116 t
        0, 0, 1, 0, 0,
        0, 0, 1, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 1,
        # 117 u
        1, 0, 0, 1, 1,
        1, 0, 0, 1, 0,
        1, 0, 0, 1, 0,
        1, 0, 0, 1, 0,
        1, 1, 1, 1, 1,
        # 118 v
        1, 1, 0, 1, 1,
        1, 0, 0, 0, 1,
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        # 119 w
        0, 0, 1, 0, 0,
        1, 0, 1, 0, 1,
        1, 0, 1, 0, 1,
        1, 1, 1, 1, 1,
        0, 1, 0, 1, 0,
        # 120 x
        1, 1, 0, 1, 1,
        0, 1, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 1, 0,
        1, 1, 0, 1, 1,
        # 121 y
        1, 1, 0, 1, 1,
        0, 1, 0, 1, 0,
        0, 1, 1, 1, 0,
        0, 0, 1, 1, 0,
        1, 1, 1, 0, 0,
        # 122 z
        1, 1, 1, 1, 1,
        0, 0, 0, 1, 1,
        0, 1, 1, 1, 0,
        1, 1, 0, 0, 0,
        1, 1, 1, 1, 1,
        # 123 {
        0, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 0,
        # 124 |
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 1, 0,
        # 125 }
        0, 1, 1, 0, 0,
        0, 0, 1, 0, 0,
        0, 0, 1, 1, 0,
        0, 0, 1, 0, 0,
        0, 1, 1, 0, 0,
        # 126 ~
        0, 0, 0, 0, 0,
        1, 1, 0, 0, 0,
        0, 1, 1, 1, 0,
        0, 0, 0, 1, 1,
        0, 0, 0, 0, 0,
        # 127
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0, 0
    ]


class Matrix:

    LED_PIN = 27
    LED_WIDTH = 5
    LED_HEIGHT = 5
    LED_NUM = LED_WIDTH * LED_HEIGHT
    LED_BRIGHTNESS = 0.25  # max led brightness due to heating up to high temperature
    INSTA_DRAW = 0
    np = None
    ROTATE = False

    # color in hsv color model color(h, s, v)
    # h = 0 - 360
    # s = 0 - 1.0
    # v = 0 - 1.0
    color = (0, 0, 1 * LED_BRIGHTNESS)  # initial set color to white

    def __init__(self, pin):
        self.LED_PIN = pin
        self.f = Font()

    def init(self):
        self.np = neopixel.NeoPixel(Pin(self.LED_PIN), self.LED_NUM)

    def translate(self, value, inMin, inMax, outMin, outMax):
        return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin

    # convert color from hsv to rgb
    def hsv(self, h, s=1, v=1):
        h = float(h)
        s = float(s)
        v = float(v)
        h60 = h / 60.0
        h60f = math.floor(h60)
        hi = int(h60f) % 6
        f = h60 - h60f
        p = v * (1 - s)
        q = v * (1 - f * s)
        t = v * (1 - (1 - f) * s)
        r, g, b = 0, 0, 0
        if hi == 0:
            r, g, b = v, t, p
        elif hi == 1:
            r, g, b = q, v, p
        elif hi == 2:
            r, g, b = p, v, t
        elif hi == 3:
            r, g, b = p, q, v
        elif hi == 4:
            r, g, b = t, p, v
        elif hi == 5:
            r, g, b = v, p, q
        r, g, b = int(r * 255), int(g * 255), int(b * 255)
        return r, g, b

    # set color used to draw function
    def pixel_color(self, h, s=1, v=1):
        self.color = (h, s, v * self.LED_BRIGHTNESS)

    def pixel_set(self, x, y):
        self.np[x + y * self.LED_HEIGHT] = self.hsv(*self.color)
        if self.INSTA_DRAW:
            self.show()

    def pixel_clear(self, x, y):
        self.np[x + y * self.LED_HEIGHT] = (0, 0, 0)
        if self.INSTA_DRAW:
            self.show()

    def pixel_blink(self, x, y, delay_ms=100):
        previous = self.np[x + y * self.LED_HEIGHT]
        self.np[x + y * self.LED_HEIGHT] = self.hsv(*self.color)
        self.np.write()
        time.sleep_ms(delay_ms)
        self.np[x + y * self.LED_HEIGHT] = previous
        self.np.write()

    def clear_all(self):
        self.np.fill((0, 0, 0))
        if self.INSTA_DRAW:
            self.show()

    def reverse_matrix(self):
        self.np[5], self.np[9] = self.np[9], self.np[5]
        self.np[6], self.np[8] = self.np[8], self.np[6]
        self.np[15], self.np[19] = self.np[19], self.np[15]
        self.np[16], self.np[18] = self.np[18], self.np[16]

    def show(self):
        if self.LED_NUM == 25:
            self.reverse_matrix()
        self.np.write()

    # write raw rgb value buffer to matrix
    def write_buffer(self, buf):
        size = self.LED_NUM
        if len(buf) < self.LED_NUM:
            size = len(buf)
        for pix in range(0, size):
            self.np[pix] = buf[pix]
        if self.INSTA_DRAW:
            self.show()

    def pixel_breathe(self, x, y, ms_in=50, ms_out=50):
        for i in range(0, 11, 1):
            val = math.pow(2, i) - 1
            self.np[x + y * self.LED_HEIGHT] = self.hsv(
                self.color[0], self.color[1], (self.LED_BRIGHTNESS * self.translate(val, 0, 1024, 0, 1)))
            self.np.write()
            time.sleep_ms(ms_in)
        for i in range(10, -1, -1):
            val = math.pow(2, i) - 1
            self.np[x + y * self.LED_HEIGHT] = self.hsv(
                self.color[0], self.color[1], (self.LED_BRIGHTNESS * self.translate(val, 0, 1024, 0, 1)))
            self.np.write()
            time.sleep_ms(ms_out)

    def pixel_mask(self, buf):
        size = self.LED_NUM
        if len(buf) < self.LED_NUM:
            size = len(buf)
        for pix in range(0, size):
            if buf[pix]:
                self.np[pix] = self.hsv(*self.color)
        if self.INSTA_DRAW:
            self.show()

    def pixel_mask_raw(self, buf):
        size = self.LED_NUM
        led_num = 0
        for i in range(0, len(buf), 3):
            if led_num == size:
                break
            self.np[led_num] = (int(buf[i]), int(
                buf[i+1]), int(buf[i+2]))
            led_num += 1
        if self.INSTA_DRAW:
            self.show()

    def _add(self, a, b):
        w_a = int(len(a)/5)
        w_b = int(len(b)/5)
        out = [0] * len(a)
        for i in range(len(a)):
            out[i] = a[i]
        for i in range(5):
            out[(i*w_a+i*w_b):(i*w_a+i*w_b)] = b[(i*w_b):((i+1)*w_b)]
        return out

    def _get_frame(self, buffer, offest):
        w_buf = int(len(buffer)/5)
        out = [0] * 25
        for x in range(5):
            for y in range(5):
                out[x + y * 5] = buffer[(x+offest)+(y*w_buf)]
        return out

    def _create_pixmap(self, input):
        spacer = [0] * 5
        empty = [0] * 25
        text_buffer = [0] * 25
        for i in range(len(input)):
            text_buffer = self._add(
                text_buffer, self.f.get_letter(ord(input[len(input)-1-i])))
            text_buffer = self._add(text_buffer, spacer)
        text_buffer = self._add(text_buffer, empty)
        return text_buffer

    def text_scroll(self, text_to_scroll, delay=150):
        t_buf = self._create_pixmap(text_to_scroll)
        # scrolling pixmap on matrix
        for pos in range(int(len(t_buf)/5) - 5 + 1):
            self.clear_all()
            self.pixel_mask(self._get_frame(t_buf, pos))
            self.show()
            time.sleep_ms(delay)


class LedMatrix:
    """
    @desc: LED Matrix interface for BeE board using WS2812B NeoPixel LEDs
           Supports text scrolling, animations, color control, and predefined patterns

    @example:
        >>> matrix = LedMatrix(PORT1)
        >>> matrix.color(120, 1, 1)  # Set green color
        >>> matrix.text("Hi!")  # Scroll text
        >>> matrix.picture(LedMatrix.SMILE)  # Show smile emoji

    @version: 1.2.0
    """

    # Predefined emoticon patterns
    SMILE = [
        1, 1, 0, 1, 1,
        1, 1, 0, 1, 1,
        0, 0, 0, 0, 0,
        1, 0, 0, 0, 1,
        0, 1, 1, 1, 0
    ]

    NORMAL = [
        1, 1, 0, 1, 1,
        1, 1, 0, 1, 1,
        0, 0, 0, 0, 0,
        1, 1, 1, 1, 1,
        0, 0, 0, 0, 0
    ]

    SAD = [
        1, 1, 0, 1, 1,
        1, 1, 0, 1, 1,
        0, 0, 0, 0, 0,
        0, 1, 1, 1, 0,
        1, 0, 0, 0, 1
    ]

    HEART = [
        0, 1, 0, 1, 0,
        1, 0, 1, 0, 1,
        1, 0, 0, 0, 1,
        0, 1, 0, 1, 0,
        0, 0, 1, 0, 0
    ]

    # Display position constants
    CENTER = 0
    LEFT = 1
    RIGHT = 2

    def __init__(self, port = None):
        """
        @desc: Initialize LED Matrix with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> matrix = LedMatrix(port=bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__m = Matrix(port.use_pins(1))
        self.__d = Digits()
        self.__f = Font()
        self.__m.init()

    def off(self) -> None:
        """
        @desc: Turn off all LEDs and update display

        @example:
            >>> matrix.off()
        """
        self.__m.clear_all()
        self.__m.show()

    def clear(self) -> None:
        """
        @desc: Clear all LEDs without updating display
               Use show() to update display after clearing

        @example:
            >>> matrix.clear()
            >>> # Do something else
            >>> matrix.show()
        """
        self.__m.clear_all()

    def show(self) -> None:
        """
        @desc: Update display with current LED states

        @example:
            >>> matrix.clear()
            >>> matrix.picture(LedMatrix.SMILE)
            >>> matrix.show()
        """
        self.__m.show()

    def text(self, message: str = "", delay_ms: int = 300) -> None:
        """
        @desc: Scroll text message across the LED matrix

        @args:
            - message: Text to display (default: "")
            - delay_ms: Scroll speed in milliseconds (default: 300)

        @example:
            >>> matrix.text("Hello!")  # Default speed
            >>> matrix.text("Fast!", 100)  # Faster scroll
        """
        self.__m.text_scroll(message, delay_ms)

    def color_rgb(self, r: int = 255, g: int = 255, b: int = 255) -> None:
        """
        @desc: Set color for subsequent LED operations using RGB color model

        @args:
            - r: Red component (0-255)
            - g: Green component (0-255)
            - b: Blue component (0-255)

        @note: 
            Common RGB values:
            - Red: (255, 0, 0)
            - Green: (0, 255, 0)
            - Blue: (0, 0, 255)
            - Yellow: (255, 255, 0)
            - Purple: (255, 0, 255)
            - White: (255, 255, 255)

        @example:
            >>> matrix.color(255, 0, 0)  # Bright red
            >>> matrix.color(0, 128, 0)  # Half-bright green
        """
        # Validate RGB values
        r = max(0, min(255, r))
        g = max(0, min(255, g))
        b = max(0, min(255, b))
        
        # Convert RGB to HSV
        r, g, b = r/255.0, g/255.0, b/255.0
        mx = max(r, g, b)
        mn = min(r, g, b)
        df = mx - mn
        
        # Calculate hue
        if mx == mn:
            h = 0
        elif mx == r:
            h = (60 * ((g-b)/df) + 360) % 360
        elif mx == g:
            h = (60 * ((b-r)/df) + 120) % 360
        elif mx == b:
            h = (60 * ((r-g)/df) + 240) % 360
            
        # Calculate saturation
        s = 0 if mx == 0 else df/mx
        
        # Value is maximum of r,g,b
        v = mx
        
        # Set the color using HSV values
        self.__m.pixel_color(h, s, v)

    def color(self, h: int = 0, s: float = 1, v: float = 1) -> None:
        """
        @desc: Set color for subsequent LED operations using HSV color model

        @args:
            - h: Hue (0-360 degrees)
            - s: Saturation (0.0-1.0)
            - v: Value/Brightness (0.0-1.0)

        @note: 
            Common hue values:
            - Red: 0
            - Green: 120
            - Blue: 240
            - Yellow: 60
            - Purple: 300

        @example:
            >>> matrix.color(0, 1, 1)  # Bright red
            >>> matrix.color(120, 1, 0.5)  # Half-bright green
        """
        self.__m.pixel_color(h, s, v)

    def blink(self, x: int, y: int, delay_ms: int) -> None:
        """
        @desc: Blink single LED at specified position

        @args:
            - x: X coordinate (0-4)
            - y: Y coordinate (0-4)
            - delay_ms: Blink duration in milliseconds

        @example:
            >>> matrix.color(0, 1, 1)  # Set red color
            >>> matrix.blink(2, 2, 500)  # Blink center LED
        """
        self.__m.pixel_blink(x, y, delay_ms)

    def breathe(self, x: int, y: int, delay_ms: int) -> None:
        """
        @desc: Create breathing effect on single LED with fade in/out

        @args:
            - x: X coordinate (0-4)
            - y: Y coordinate (0-4)
            - delay_ms: Speed of breath effect in milliseconds

        @example:
            >>> matrix.color(240, 1, 1)  # Set blue color
            >>> matrix.breathe(2, 2, 50)  # Breathing effect
        """
        self.__m.pixel_breathe(x, y, delay_ms, delay_ms)

    def picture(self, buf) -> None:
        """
        @desc: Display pattern on LED matrix

        @args:
            - buf: Pattern data in one of three formats:
                1. List of 25 binary values (0/1)
                2. 5 bytes for collection mode
                3. Raw RGB bytes for pick mode

        @example:
            >>> matrix.picture(LedMatrix.SMILE)  # Show smile
            >>> matrix.picture([1] * 25)  # All LEDs on
            >>> matrix.picture(b'\xff\xff\xff\xff\xff')  # All on (bytes)
        """
        self.__m.clear_all()
        if isinstance(buf, bytes):
            if len(buf) == 5:
                # Collection mode
                temp = []
                for i in buf:
                    l = "{0:b}".format(i)
                    while len(l) < 5:
                        l = "0" + l
                    for j in l:
                        temp.append(int(j))
                self.__m.pixel_mask(temp)
            else:
                # Pick mode
                self.__m.pixel_mask_raw(buf)
        elif isinstance(buf, list):
            self.__m.pixel_mask(buf)
        self.__m.show()

    def digit(self, num: int, pos: int = 0) -> None:
        """
        @desc: Display single digit on LED matrix

        @args:
            - num: Digit to display (0-9)
            - pos: Position (CENTER=0, LEFT=1, RIGHT=2)

        @example:
            >>> matrix.digit(5)  # Center-aligned 5
            >>> matrix.digit(3, LedMatrix.LEFT)  # Left-aligned 3
        """
        if pos == self.CENTER:
            pic = self.__d.get_digit(num, self.__d.digits_center)
        elif pos == self.LEFT:
            pic = self.__d.get_digit(num, self.__d.digits_left)
        else:
            pic = self.__d.get_digit(num, self.__d.digits_right)
        self.__m.clear_all()
        self.__m.pixel_mask(pic)
        self.__m.show()
