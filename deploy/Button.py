import utime
from machine import Pin
from micropython import const

class Button:
    """
    @desc: Button interface for BeE board with debounce and event handling
           Supports press, release, and long-press detection with callbacks

    @example:
        >>> from BeeBrain import bee
        >>> button = Button(bee.PORT1)  # Using onboard button A
        >>> button.on_press(lambda p: print("Button pressed"))
        >>> button.on_long_press(lambda p: print("Long press detected"), 2000)
    """
    HIGH = const(1)
    LOW = const(0)

    PULL_UP = const(1)
    PULL_DOWN = const(0)

    def __init__(self, 
                 port = None, 
                 btn_pull: int = PULL_UP, 
                 debounce_ms: int = 200, 
                 btn_pin = None):
        """
        @desc: Initialize button with specified pin and configuration

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - btn_pull (int): Pull-up/down resistor configuration
                * PULL_UP: But<PERSON> connects to ground when pressed (default)
                * PULL_DOWN: <PERSON><PERSON> connects to VCC when pressed
            - debounce_ms (int): Debounce time in milliseconds, defaults to 100ms
            - btn_pin (int): GPIO pin number for custom button

        @example:
            >>> button = Button(bee.PORT1)  # Default pull-up configuration
            >>> button = Button(bee.PORT1, Button.PULL_DOWN)  # Pull-down configuration
        """
        self.__pin = None
        if port is not None:
            self.__pin = port.use_pins(1)
        if btn_pin is not None:
            self.__pin = btn_pin
        if not self.__pin:
            raise ValueError("Invalid port")
        if not 0 <= debounce_ms <= 1000:
            raise ValueError("Debounce time must be between 0 and 1000ms")
        self.debounce_ms = debounce_ms
        self.last_press_time = 0
        self.press_start_time = 0
        self._on_press = None
        self._on_release = None
        self._on_long_press = None
        self._long_press_duration = 1000  # ms

        if btn_pull == self.PULL_UP:
            self.__button = Pin(self.__pin, Pin.IN, Pin.PULL_UP)
            self._pressed_state = self.LOW
        else:
            self.__button = Pin(self.__pin, Pin.IN, Pin.PULL_DOWN)
            self._pressed_state = self.HIGH

        # External button is always pull-down
        if port is not None:
            self._pressed_state = self.HIGH

        self.__button.irq(trigger=Pin.IRQ_FALLING | Pin.IRQ_RISING, handler=self.__internal_handler)

    def __internal_handler(self, pin):
        """
        @desc: Internal IRQ handler for button state changes
               Manages debouncing and calls appropriate callbacks
        
        @args:
            - pin: Pin object that triggered the IRQ
        """
        current_time = utime.ticks_ms()
        if utime.ticks_diff(current_time, self.last_press_time) < self.debounce_ms:
            return
        self.last_press_time = current_time

        if self.is_pressed():
            self.press_start_time = current_time
            if self._on_press:
                self._on_press(pin)
        else:
            press_duration = utime.ticks_diff(current_time, self.press_start_time)
            if self._on_release:
                self._on_release(pin)
            if self._on_long_press and press_duration >= self._long_press_duration:
                self._on_long_press(pin)

    def is_pressed(self) -> bool:
        """
        @desc: Check if button is currently pressed

        @returns:
            bool: True if button is pressed, False otherwise

        @example:
            >>> if button.is_pressed():
            ...     print("Button is currently pressed")
        """
        return self.__button.value() == self._pressed_state

    def is_released(self) -> bool:
        """
        @desc: Check if button is currently released

        @returns:
            bool: True if button is released, False otherwise

        @example:
            >>> if button.is_released():
            ...     print("Button is currently released")
        """
        return not self.is_pressed()
    
    def is_long_pressed(self) -> bool:
        """
        @desc: Check if button is currently long-pressed

        @returns:
            bool: True if button is long-pressed, False otherwise

        @example:
            >>> if button.is_long_pressed():
            ...     print("Button is currently long-pressed")
        """
        return self.is_pressed() and utime.ticks_diff(utime.ticks_ms(), self.press_start_time) >= self._long_press_duration

    @property
    def value(self) -> int:
        """
        @desc: Get raw pin value

        @returns:
            int: Current pin value (0 or 1)
        """
        return self.__button.value()

    def disable_irq(self):
        """
        @desc: Disable interrupt handler for this button
               Useful when button monitoring is no longer needed

        @example:
            >>> button.disable_irq()  # Stop monitoring button events
        """
        self.__button.irq(handler=None)

    def on_press(self, callback):
        """
        @desc: Register callback function for button press event

        @args:
            - callback: Function to call when button is pressed
                        Function should accept one parameter (pin)

        @raises:
            - TypeError: If callback is not callable

        @example:
            >>> button.on_press(lambda pin: print("Button pressed"))
        """
        if callable(callback):
            self._on_press = callback
        else:
            raise TypeError("Callback must be callable")

    def on_release(self, callback):
        """
        @desc: Register callback function for button release event

        @args:
            - callback: Function to call when button is released
                        Function should accept one parameter (pin)

        @raises:
            - TypeError: If callback is not callable

        @example:
            >>> button.on_release(lambda pin: print("Button released"))
        """
        if callable(callback):
            self._on_release = callback
        else:
            raise TypeError("Callback must be callable")

    def on_long_press(self, callback, duration_ms=1000):
        """
        @desc: Register callback function for button long-press event

        @args:
            - callback: Function to call when button is long-pressed
                        Function should accept one parameter (pin)
            - duration_ms (int): Duration in milliseconds to consider as long press
                                 Defaults to 1000ms (1 second)

        @raises:
            - TypeError: If callback is not callable

        @example:
            >>> button.on_long_press(lambda pin: print("Long press detected"), 2000)
        """
        if callable(callback):
            self._on_long_press = callback
            self._long_press_duration = duration_ms
        else:
            raise TypeError("Callback must be callable")
