import neopixel

from machine import Pin


class Neopixel:
    """
    @desc: Controls NeoPixel LED strips/arrays on BeE board
    
    @example:
        >>> neo = BeeNeopixel(pin=5, number_of_led=2)
        >>> neo.set_rgb(0, 255, 0, 0)  # Set first LED to red
    
    @version: 1.0.0
    """

    def __init__(self, port = None, number_of_led: int = 2, pin = None):
        """
        @desc: Initialize NeoPixel controller
        
        @args:
            - port: BeE board port object (PORT1-PORT6)
            - number_of_led (int): Total number of LEDs in the strip/array
            - pin (int): GPIO pin number connected to NeoPixel data l ine
        
        @raises:
            - ValueError: If pin number is invalid
            - TypeError: If arguments are not integers
        
        @example:
            >>> neo = BeeNeopixel(pin=5, number_of_led=2)
        """
        self.__pin = None
        if port is not None:
            self.__pin = port.use_pins(1)
        if pin is not None:
            self.__pin = pin
        if not self.__pin:
            raise ValueError("Invalid port")
        self.__number_of_led = number_of_led
        self.__neo = neopixel.NeoPixel(Pin(self.__pin), self.__number_of_led)

    def _validate_color(self, color: int) -> int:
        """
        @desc: Validates and clamps RGB color value to ensure it's within valid range

        @args:
            - color (int): RGB color component value to validate

        @raises:
            - TypeError: If color is not an integer

        @returns:
            int: Normalized color value between 0 and 255

        @example:
            >>> _validate_color(-10)
            0
            >>> _validate_color(300)
            255
            >>> _validate_color(128)
            128
        """
        if color < 0:
            return 0
        if color > 255:
            return 255
        return color

    def _hex_to_rgb(self, hex_color: str) -> tuple[int, int, int]:
        """
        @desc: Converts hexadecimal color string to RGB values

        @args:
            - hex_color (str): Color in hexadecimal format (e.g., '#FF0000' for red)

        @raises:
            - ValueError: If hex_color format is invalid
            - TypeError: If hex_color is not a string

        @returns:
            tuple[int, int, int]: RGB color values, each in range [0-255]

        @example:
            >>> _hex_to_rgb('#FF0000')
            (255, 0, 0)
            >>> _hex_to_rgb('#00FF00')
            (0, 255, 0)
        """
        return tuple(int(hex_color[i:i+2], 16) for i in (1, 3, 5))

    def set_rgb(self, led_id: int, r: int, g: int, b: int) -> None:
        """
        @desc: Sets specific LED to given RGB color values

        @args:
            - led_id (int): Index of LED to control (0-based)
            - r (int): Red component [0-255]
            - g (int): Green component [0-255]
            - b (int): Blue component [0-255]

        @raises:
            - IndexError: If led_id is out of range
            - TypeError: If color values are not integers

        @example:
            >>> neo.set_rgb(0, 255, 0, 0)  # Set first LED to red
            >>> neo.set_rgb(1, 0, 255, 0)  # Set second LED to green
        """
        r = self._validate_color(r)
        g = self._validate_color(g)
        b = self._validate_color(b)

        self.__neo[led_id] = (r, g, b)
        self.__neo.write()

    def get_rgb(self, led_id: int) -> tuple[int, int, int]:
        """
        @desc: Retrieves RGB color values of specific LED

        @args:
            - led_id (int): Index of LED to query (0-based)

        @raises:
            - IndexError: If led_id is out of range
            - TypeError: If led_id is not an integer

        @returns:
            tuple[int, int, int]: RGB color values of specified LED

        @example:
            >>> neo.get_rgb(0)
            (255, 0, 0)
            >>> neo.get_rgb(1)
            (0, 255, 0)
        """
        return self.__neo[led_id]

    def set_hex(self, led_id: int, hex_color: str) -> None:
        """
        @desc: Sets specific LED color using hexadecimal color value

        @args:
            - led_id (int): Index of LED to control (0-based)
            - hex_color (str): Color in hexadecimal format (e.g., '#FF0000' for red)

        @raises:
            - IndexError: If led_id is out of range
            - ValueError: If hex_color format is invalid
            - TypeError: If arguments have wrong types

        @example:
            >>> neo.set_hex(0, '#FF0000')  # Set first LED to red
            >>> neo.set_hex(1, '#00FF00')  # Set second LED to green
        """
        r, g, b = self._hex_to_rgb(hex_color)
        self.set_rgb(led_id, r, g, b)

    def off(self, led_id: int) -> None:
        """
        @desc: Turns off specific LED by setting its color to black (0,0,0)

        @args:
            - led_id (int): Index of LED to turn off (0-based)

        @raises:
            - IndexError: If led_id is out of range
            - TypeError: If led_id is not an integer

        @example:
            >>> neo.off(0)  # Turn off first LED
            >>> neo.off(1)  # Turn off second LED
        """
        self.__neo[led_id] = (0, 0, 0)
        self.__neo.write()
