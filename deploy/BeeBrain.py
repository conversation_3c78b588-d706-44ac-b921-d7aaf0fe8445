import time
import random
import network
import bluetooth

from micropython import const
from machine import Pin, ADC, PWM
from umqtt.simple import MQ<PERSON><PERSON>lient

# from BeeBluetooth import <PERSON><PERSON><PERSON> # Not implemented yet
from Button import Button
from Neopixel import Neopixel
from Oled import Oled
from Buzzer import Buzzer
from Mpu6050 import MPU6050
from Gamepad import GamepadMQTT
from MotorOnBoard import Motor, Servo
from INA219 import INA219


class Port:
    """
    @desc: Represents a physical port on the BeE board with two pins
    
    @args:
        - pin1 (int): First pin number of the port
        - pin2 (int): Second pin number of the port
    
    @example:
        >>> port = Port(22, 21)  # Create PORT1
        >>> pin1, pin2 = port.use_pins()  # Get both pins
    
    @version: 1.0.0
    """

    def __init__(self, pin1: int, pin2: int):
        self.__pin1 = pin1
        self.__pin2 = pin2

    def use_pins(self, index: int = 0):
        """
        @desc: Return pin(s) of port based on index

        @args:
            - index (int): Pin selection (0: all pins, 1: first pin, 2: second pin)

        @returns:
            Union[int, tuple[int, int]]: Single pin number or tuple of both pins

        @example:
            >>> port = Port(22, 21)
            >>> port.use_pins()  # Returns (22, 21)
            >>> port.use_pins(1)  # Returns 22
            >>> port.use_pins(2)  # Returns 21
        """
        if index == 1:
            return self.__pin1
        elif index == 2:
            return self.__pin2
        return self.__pin1, self.__pin2
    

class LedOnBoard:
    """
    @desc: Control LedOnBoard
    
    @args:
        - neopixel_controller (Neopixel): Neopixel controller object
        - led_id (int): LED index (0 or 1)
    """
    def __init__(self, neopixel_controller, led_id: int):
        self.__neopixel = neopixel_controller
        self.__led_id = led_id

    def on(self) -> None:
        """
        @desc: Turn LED on by setting it to white (255, 255, 255)
        """
        self.__neopixel.set_rgb(self.__led_id, 255, 255, 255)

    def off(self) -> None:
        """
        @desc: Turn LED off by setting it to black (0, 0, 0)
        """
        self.__neopixel.set_rgb(self.__led_id, 0, 0, 0)

    def set_rgb(self, r: int, g: int, b: int) -> None:
        """
        @desc: Set LED color using RGB values

        @args:
            - r (int): Red component (0-255)
            - g (int): Green component (0-255)
            - b (int): Blue component (0-255)

        @example:
            >>> led.set_rgb(255, 0, 0)  # Set LED to red
        """
        self.__neopixel.set_rgb(self.__led_id, r, g, b)
    
    def set_hsv(self, h: int, s: int, v: int) -> None:
        """
        @desc: Set LED color using HSV values

        @args:
            - h (int): Hue component (0-360)
            - s (int): Saturation component (0-100)
            - v (int): Value/Brightness component (0-100)

        @example:
            >>> led.set_hsv(120, 100, 100)  # Set LED to green
        """
        self.__neopixel.set_hsv(self.__led_id, h, s, v)

    def toggle(self) -> None:
        """
        @desc: Toggle LED state between on and off
        """
        if self.__neopixel.get_rgb(self.__led_id) == (0, 0, 0):
            self.on()
        else:
            self.off()


class MotorOnBoard:
    """
    @desc: Control DC motor on BeE board by individual motor

    @args:
        - motor_controller: BeeBrain.dcmotor
        - motor_id: BeeBrain.M1 or BeeBrain.M2
    """
    def __init__(self, motor_controller, motor_id):
        self.__motor = motor_controller
        self.__motor_id = motor_id

    def speed(self, power: int) -> None:
        """
        @desc: Set speed of motor, max speed is 50%
        @args:
            - power: -100 to 100 (negative for reverse)
        """
        self.__motor.speed(self.__motor_id, power)

    def brake(self) -> None:
        """
        @desc: Stop motor by braking
        """
        self.__motor.brake(self.__motor_id)


class ServoOnBoard:
    """
    @desc: Control servo on BeE board by individual servo

    @args:
        - servo_controller: BeeBrain.servo
        - servo_id: BeeBrain.S1 or BeeBrain.S2 or BeeBrain.S3 or BeeBrain.S4
    """
    def __init__(self, servo_controller, servo_id):
        self.__servo = servo_controller
        self.__servo_id = servo_id

    def position(self, degrees: int) -> None:
        """
        @desc: Set position of servo
        @args:
            - degrees: 0 to 270
        """
        self.__servo.position(self.__servo_id, degrees)

    def release(self) -> None:
        """
        @desc: Release servo
        """
        self.__servo.release(self.__servo_id)


class BeeBrain:
    """
    @desc: Main controller class for BeE board, managing all onboard components
           and providing high-level control interfaces

    @soc: esp32
    @requires: 
        - Button, Neopixel, Oled, Buzzer
        - MotorOnBoard, Mpu6050 modules

    @example:
        >>> bee = BeeBrain()
        >>> bee.move_forward(50, 2)  # Move forward at 50% power for 2 seconds

    @version: 1.24.0
    """

    __ONBOARD_LED = const(48)
    __ONBOARD_BTNA = const(1)
    __ONBOARD_BTNB = const(2)
    __BUZZER_PIN = const(7)

    # PORT 0
    __P0_1 = const(9)  # I2C only: SDA
    __P0_2 = const(8)  # I2C only: SCL

    # PORT 1
    __P1_1 = const(5)  # I2C only
    __P1_2 = const(4)  # I2C only

    # PORT 2
    __P2_1 = const(14)  # I2C, ADC, PWM
    __P2_2 = const(6)  # I2C, ADC, PWM

    # PORT 3
    __P3_1 = const(16)  # I2C, ADC, PWM
    __P3_2 = const(15)  # I2C, ADC, PWM

    # PORT 4
    __P4_1 = const(13)  # I2C, ADC, PWM
    __P4_2 = const(12)  # I2C, PWM

    # PORT 5
    __P5_1 = const(18)  # I2C, ADC, PWM
    __P5_2 = const(17)  # I2C, ADC, PWM

    # PORT 6
    __P6_1 = const(11)  # I2C, PWM
    __P6_2 = const(10)  # I2C, PWM

    # PORT
    __PORT0 = Port(__P0_1, __P0_2)
    __PORT1 = Port(__P1_1, __P1_2)
    __PORT2 = Port(__P2_1, __P2_2)
    __PORT3 = Port(__P3_1, __P3_2)
    __PORT4 = Port(__P4_1, __P4_2)
    __PORT5 = Port(__P5_1, __P5_2)
    __PORT6 = Port(__P6_1, __P6_2)

    # State
    ON = const(1)
    OFF = const(0)

    # Button
    __BOTH_BUTTON = const(0)
    __BUTTON_A = const(1)
    __BUTTON_B = const(2)

    # Neopixel
    __LED1 = const(0)
    __LED2 = const(1)

    # Motor
    __AIN1 = const(41)
    __AIN2 = const(42)
    __PWMA = const(39)

    __BIN1 = const(35)
    __BIN2 = const(36)
    __PWMB = const(40)

    __M1 = const(0)
    __M2 = const(1)

    FORWARD = const(0)
    BACKWARD = const(1)
    LEFT = const(2)
    RIGHT = const(3)

    # Servo
    __S1_PIN = const(21)
    __S2_PIN = const(47)
    __S3_PIN = const(38)
    __S4_PIN = const(37)

    __S1 = const(0)
    __S2 = const(1)
    __S3 = const(2)
    __S4 = const(3)

    # VALUE or PERCENT
    VALUE = 0
    PERCENT = 1

    def __init__(self) -> None:
        """
        @desc: Initialize BeeBrain with all onboard components:
               Buttons, Neopixel, OLED, Buzzer, Motors, IMU, etc.

        @raises:
            - RuntimeError: If hardware initialization fails
            - ImportError: If required modules are missing

        @example:
            >>> from BeeBrain import bee
            >>> bee = BeeBrain()  # Initializes all components
        """
        # Initialize onboard buttons
        self.buttonA = Button(btn_pin=__ONBOARD_BTNA)
        self.buttonB = Button(btn_pin=__ONBOARD_BTNB)

        # Initialize onboard LED
        self.__neopixel = Neopixel(pin=__ONBOARD_LED, number_of_led=2)
        self.led1 = LedOnBoard(self.__neopixel, __LED1)
        self.led2 = LedOnBoard(self.__neopixel, __LED2)

        # Initialize onboard buzzer
        self.buzzer = Buzzer(pin=__BUZZER_PIN)

        # Initialize onboard oled
        try:
            self.display = Oled(self.__PORT0, rotation=180)
        except Exception as e:
            self.display = None
            print("OLED not found")

        # Initialize onboard IMU
        try:
            self.imu = MPU6050(self.__PORT0)
        except:
            self.imu = None
            print("MPU6050 not found")

        # Initialize onboard motors
        self.__dcmotor = Motor(
            ain1_pin=__AIN1, ain2_pin=__AIN2, pwma_pin=__PWMA,
            bin1_pin=__BIN1, bin2_pin=__BIN2, pwmb_pin=__PWMB
        )
        self.motor1 = MotorOnBoard(self.__dcmotor, __M1)
        self.motor2 = MotorOnBoard(self.__dcmotor, __M2)

        # Initialize onboard servos
        self.__servo = Servo(
            s1_pin=__S1_PIN, s2_pin=__S2_PIN, 
            s3_pin=__S3_PIN, s4_pin=__S4_PIN
        )
        self.servo1 = ServoOnBoard(self.__servo, __S1)
        self.servo2 = ServoOnBoard(self.__servo, __S2)
        self.servo3 = ServoOnBoard(self.__servo, __S3)
        self.servo4 = ServoOnBoard(self.__servo, __S4)

        try:
            self.battery = INA219(self.__PORT0)
        except:
            self.battery = None
            print("INA219 not found")

        # Initialize BeE
        self.init_bee()

    @property
    def PORT0(self):
        return self.__PORT0

    @property
    def PORT1(self):
        return self.__PORT1

    @property
    def PORT2(self):
        return self.__PORT2

    @property
    def PORT3(self):
        return self.__PORT3

    @property
    def PORT4(self):
        return self.__PORT4

    @property
    def PORT5(self):
        return self.__PORT5

    @property
    def PORT6(self):
        return self.__PORT6

    """
    Button
    """
    def is_button_pressed(self, button: int = __BOTH_BUTTON):
        """
        Return True if a button (A|B|A+B) is pressed
        """
        if button == __BUTTON_A:
            return self.buttonA.is_pressed()
        elif button == __BUTTON_B:
            return self.buttonB.is_pressed()
        elif button == __BOTH_BUTTON:
            return self.buttonA.is_pressed() or self.buttonB.is_pressed()

    def is_button_released(self, button: int = __BOTH_BUTTON):
        """
        Return True if a button (A|B|A+B) is released
        """
        if button == __BUTTON_A:
            return self.buttonA.is_released()
        elif button == __BUTTON_B:
            return self.buttonB.is_pressed()
        elif button == __BOTH_BUTTON:
            return self.buttonA.is_pressed() or self.buttonB.is_pressed()

    """
    DC Motor
    """

    def move_robot(self, move_dir: str, move_power: int, move_time: float = 0) -> None:
        """
        @desc: Set movement of robot with %power in time.
        @requires: BeeBrain module
        @args:
            - move_dir: [FORWARD, BACKWARD]
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        if move_dir == self.FORWARD:
            # for i in range(0, move_power, 2):
            self.__dcmotor.speed(0, move_power)
            self.__dcmotor.speed(1, move_power)
            # time.sleep(0.001)
        else:
            # for i in range(0, move_power, 2):
            self.__dcmotor.speed(0, -move_power)
            self.__dcmotor.speed(1, -move_power)
            # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def turn_robot(self, move_dir: str, move_power: int, move_time: float = 0) -> None:
        """
        @desc: Turn robot left/right around the center with %power in time.
        @requires: BeeBrain module
        @args:
            - move_dir: [LEFT, RIGHT]
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        if move_dir == self.LEFT:
            # for i in range(0, move_power, 2):
            self.__dcmotor.speed(0, -move_power)
            self.__dcmotor.speed(1, move_power)
            # time.sleep(0.001)

        if move_dir == self.RIGHT:
            # for i in range(0, move_power, 2):
            self.__dcmotor.speed(0, move_power)
            self.__dcmotor.speed(1, -move_power)
            # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def move_forward(self, move_power: int, move_time: float = 0):
        """
        @desc: Set movement of robot forward with %power in time.
        @requires: BeeBrain module
        @args:
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        # for i in range(0, move_power, 2):
        self.__dcmotor.speed(0, move_power)
        self.__dcmotor.speed(1, move_power)
        # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def move_backward(self, move_power: int, move_time: float = 0):
        """
        @desc: Set movement of robot backward with %power in time.
        @requires: BeeBrain module
        @args:
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        # for i in range(0, move_power, 2):
        self.__dcmotor.speed(0, -move_power)
        self.__dcmotor.speed(1, -move_power)
        # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def turn_left(self, move_power: int, move_time: float = 0) -> None:
        """
        @desc: Turn robot left around the center with %power in time.
        @requires: BeeBrain module
        @args:
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        # for i in range(0, move_power, 2):
        self.__dcmotor.speed(0, -move_power)
        self.__dcmotor.speed(1, move_power)
        # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def turn_right(self, move_power: int, move_time: float = 0) -> None:
        """
        @desc: Turn robot right around the center with %power in time.
        @requires: BeeBrain module
        @args:
            - move_power: [0-100]% power
            - move_time: seconds
        @returns:
            None
        """
        if move_power > 100:
            move_power = 100
        elif move_power < 0:
            move_power = 0
        # for i in range(0, move_power, 2):
        self.__dcmotor.speed(0, move_power)
        self.__dcmotor.speed(1, -move_power)
        # time.sleep(0.001)

        if move_time > 0:
            time.sleep(move_time)
            self.stop_robot()

    def stop_robot(self):
        """
        @desc: Stop the robot
        @requires: BeeBrain module
        @args:
            None
        @returns:
            None
        """
        self.__dcmotor.brake(0)
        self.__dcmotor.brake(1)

    """
    Network
    """

    def connect_wifi(self, ssid: str, password: str) -> network.WLAN:
        """
        @desc: Connects to WiFi network and displays connection status on OLED

        @args:
            - ssid (str): WiFi network name
            - password (str): WiFi password

        @raises:
            - OSError: If connection fails
            - RuntimeError: If WiFi hardware is not available

        @returns:
            network.WLAN: Connected network interface

        @example:
            >>> sta_if = bee.connect_wifi("MyNetwork", "password123")
            >>> print(sta_if.ifconfig())  # Show IP configuration
        """
        sta_if = network.WLAN(network.STA_IF)
        if not sta_if.isconnected():
            self.display.clear()
            self.display.text('Connecting to...', 0, 0, 1)
            self.display.text(ssid, 12, 12, 1)
            self.display.show()
            sta_if.active(True)
            try:
                sta_if.connect(ssid, password)
            except OSError as er:
                sta_if.disconnect()
                sta_if.connect(ssid, password)
            while not sta_if.isconnected():
                pass
            self.display.clear()
            self.display.text('Connected to...', 0, 0, 1)
            self.display.text(ssid, 0, 12, 1)
            self.display.text(sta_if.ifconfig()[0], 0, 24, 1)
            self.display.show()
        return sta_if

    def connect_mqtt(self, user: str, password: str, url: str = 'beeblock.vn', port: int = 1883) -> MQTTClient:
        """
        @desc: Creates and returns an MQTT client connection

        @args:
            - user (str): MQTT username
            - password (str): MQTT password

        @returns:
            MQTTClient: Configured MQTT client instance

        @example:
            >>> mqtt = bee.connect_mqtt("user123", "pass123")
            >>> mqtt.connect()
        """
        device = random.randint(0, 1000000)
        client_id = f'bee-{device}' 
        client = MQTTClient(client_id, url, port, user, password)
        return client

    # def connect_ble(self, device_name: str = "BeeBLE") -> BeeBLE:
    #     """
    #     @desc: Initializes and returns a Bluetooth Low Energy connection

    #     @args:
    #         - device_name (str): Name for BLE device, defaults to "BeeBLE"

    #     @returns:
    #         BeeBLE: Configured BLE interface

    #     @example:
    #         >>> ble = bee.connect_ble("MyBot")
    #         >>> ble.advertise()
    #     """
    #     ble = bluetooth.BLE()
    #     return BeeBLE(ble, device_name)
    
    def connect_gamepad(self, ip: str = "") -> GamepadMQTT:
        """
        @desc: Connects to gamepad MQTT server

        @args:
            - ip (str): IP address of the BeE device, defaults to ""

        @returns:
            GamepadMQTT: Configured gamepad interface

        @example:
            >>> gamepad = bee.connect_gamepad("*************")
            >>> gamepad.check_msg()
        """
        self.display.clear()
        self.display.text("Gamepad IP:", 23, 15, 1)
        self.display.text(ip, 15, 27, 1)
        self.display.show()
        return GamepadMQTT(ip)

    """
    GPIO
    """

    def digital_write(self, port, value):
        pin = Pin(port.use_pins(1), Pin.OUT)
        pin.value(value)

    def digital_read(self, port) -> int:
        pin = Pin(port.use_pins(1), Pin.IN)
        return pin.value()

    """
    ADC
    """

    def analog_read(self, port, value_or_percent: int = VALUE) -> int:
        pin = ADC(port.use_pins(1))
        if value_or_percent == self.VALUE:
            return pin.read()
        elif value_or_percent == self.PERCENT:
            return int(pin.read() / 4095.0 * 100)

    """
    PWM
    """

    def pwm_write(self, port, pwm: int, freq: int = 1000):
        """
        @desc: pwm write with duty: [0-100] and default freq: 1kHz
        """
        duty = int(pwm * 1023 / 100.0)
        PWM(Pin(port.use_pins(1)), freq=freq, duty=duty)

    """
    Start
    """

    def init_bee(self) -> None:
        """
        @desc: Initializes the BeeBrain board with welcome message and default states
               Turns off all outputs and displays welcome message on OLED

        @example:
            >>> bee = BeeBrain()  # init_bee() called automatically
        """
        self.buzzer.be_quiet()
        # Turn off Led
        self.led1.off()
        self.led2.off()
        # Turn off motor
        if self.__dcmotor:
            self.stop_robot()
        # Turn on Oled
        if self.display:
            self.display.clear()
            self.display.text("Hello, there", 15, 0, 1)
            self.display.text("Let's play with", 5, 12, 1)
            self.display.text("BeE board 1.26.0", 0, 35, 1)
            self.display.text("beeblock.vn", 20, 50, 1)
            self.display.show()
            time.sleep(1)
            self.display.clear()
            self.display.show()


bee = BeeBrain()
