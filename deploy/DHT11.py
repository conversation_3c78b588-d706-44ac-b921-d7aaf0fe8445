import dht
from machine import Pin


class DHT11:
    """
    @desc: Interface for DHT11 temperature and humidity sensor
           Supports reading temperature and humidity

    @example:
        >>> from BeeBrain import bee
        >>> sensor = DHT11(PORT1)
        >>> temp, humid = sensor.read()
        >>> print(f"Temperature: {temp}°C, Humidity: {humid}%")
    """

    def __init__(self, port = None):
        """
        @desc: Initialize DHT11 sensor with specified port

        @args:
            - port: BeE board port object (PORT1-PORT6)

        @example:
            >>> from BeeBrain import bee
            >>> sensor = DHT11(bee.PORT1)
        """
        if port is None:
            raise ValueError("Invalid port")
        self.__pin = port.use_pins(1)
        self.__dht = dht.DHT11(Pin(self.__pin, Pin.IN, Pin.PULL_UP))
        self.__temperature = 0
        self.__humidity = 0

    def measure(self):
        """
        @desc: Read temperature and humidity from sensor

        @returns:
            - tuple: (temperature, humidity) in Celsius and percent

        @example:
            >>> temp, humid = sensor.read()
            >>> print(f"Temperature: {temp}°C, Humidity: {humid}%")
        """
        self.__dht.measure()
        self.__temperature = self.__dht.temperature()
        self.__humidity = self.__dht.humidity()
    
    @property
    def temperature(self):
        """
        @desc: Read temperature from sensor

        @returns:
            - float: Temperature in Celsius

        @example:
            >>> temp = sensor.temperature
            >>> print(f"Temperature: {temp}°C")
        """
        return self.__temperature
    
    @property
    def humidity(self):
        """
        @desc: Read humidity from sensor

        @returns:
            - float: Humidity in percent

        @example:
            >>> humid = sensor.humidity
            >>> print(f"Humidity: {humid}%")
        """
        return self.__humidity