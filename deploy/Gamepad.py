# == Gamepad MQTT Setup ==
import json
import time
from umqtt.simple import MQTTClient

class GamepadMQTT:
    """
    @desc: Gamepad interface for BeE board using MQTT
           Supports receiving and processing gamepad commands

    @requires: 
        - umqtt.simple
        - json
        - time

    @example:
        >>> gamepad = GamepadMQTT(ip="*************")
        >>> gamepad.check_msg()
        >>> command = gamepad.read_command()
        >>> if command == "FORWARD":
        ...     bee.move_forward()
    """
    def __init__(self, 
                 ip="", 
                 server="beeblock.vn", 
                 port="1883", 
                 user="beeblock", 
                 password="BeeBlock12345"):
        """
        @desc: Initialize gamepad MQTT interface

        @args:
            - ip (str): IP address of the BeE device, defaults to ""
            - server (str): MQTT server address, defaults to "beeblock.vn"
            - port (int): MQTT server port, defaults to 1883
            - user (str): MQTT username, defaults to "beeblock"
            - password (str): MQTT password, defaults to "BeeBlock12345"
        """
        self.__mqtt = MQTTClient(ip, server, port, user, password)
        self.__topic = "bee/gamepad/commands/" + ip
        self.last_command = ""
        self.button_states = {}
        self.commands_queue = []
        
        self.__mqtt.connect()
        # Subscribe to gamepad topic
        self.__mqtt.set_callback(self._on_message)
        self.__mqtt.subscribe(self.__topic)
        
        print(f"Gamepad MQTT setup complete. Listening on topic: {self.__topic}")

    def check_msg(self):
        """
        @desc: Check for new MQTT messages
        """
        self.__mqtt.check_msg()

    def _on_message(self, topic, msg):
        # Callback for new MQTT messages
        try:
            # Parse JSON message
            data = json.loads(msg.decode())
            command = data.get('command', '').upper()

            if command:
                self.last_command = command
                self.commands_queue.append(command)
                self.button_states[command] = True
                print(f"Received gamepad command: {command}")

        except Exception as e:
            print(f"Error parsing gamepad message: {e}")

    def read_command(self):
        """
        @desc: Read next command from queue

        @returns:
            - str: Next command in queue, or empty string if queue is empty
        """
        self.__mqtt.check_msg()

        if self.commands_queue:
            return self.commands_queue.pop(0)
        return ""

    def get_last_command(self):
        """
        @desc: Get last received command

        @returns:
            - str: Last received command
        """
        return self.last_command

    def is_button_pressed(self, button):
        """
        @desc: Check if a button is currently pressed

        @args:
            - button (str): Button name to check

        @returns:
            - bool: True if button is pressed, False otherwise
        """
        return self.button_states.get(button.upper(), False)

    def clear_button_state(self, button):
        """
        @desc: Clear state of a button

        @args:
            - button (str): Button name to clear
        """
        self.button_states[button.upper()] = False

    def clear_all_states(self):
        """
        @desc: Clear state of all buttons and commands
        """
        self.button_states.clear()
        self.commands_queue.clear()