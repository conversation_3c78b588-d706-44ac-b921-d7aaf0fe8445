import math
import ustruct
import time

from machine import Pin, I2C
from micropython import const


class PCA9685:
    def __init__(self, i2c, address=0x40):
        self.i2c = i2c
        self.address = address
        self.reset()

    def _write(self, address, value):
        self.i2c.writeto_mem(self.address, address, bytearray([value]))

    def _read(self, address):
        return self.i2c.readfrom_mem(self.address, address, 1)[0]

    def reset(self):
        self._write(0x00, 0x00)  # Mode1

    def freq(self, freq=None):
        if freq is None:
            return int(25000000.0 / 4096 / (self._read(0xfe) - 0.5))
        prescale = int(25000000.0 / 4096.0 / freq + 0.5)
        old_mode = self._read(0x00)  # Mode 1
        self._write(0x00, (old_mode & 0x7F) | 0x10)  # Mode 1, sleep
        self._write(0xfe, prescale)  # Prescale
        self._write(0x00, old_mode)  # Mode 1
        time.sleep_us(5)
        self._write(0x00, old_mode | 0xa1)  # Mode 1, autoincrement on

    def pwm(self, index, on=None, off=None):
        if on is None or off is None:
            data = self.i2c.readfrom_mem(self.address, 0x06 + 4 * index, 4)
            return ustruct.unpack('<HH', data)
        data = ustruct.pack('<HH', on, off)
        self.i2c.writeto_mem(self.address, 0x06 + 4 * index,  data)

    def duty(self, index, value=None, invert=False):
        if value is None:
            pwm = self.pwm(index)
            if pwm == (0, 4096):
                value = 0
            elif pwm == (4096, 0):
                value = 4095
            value = pwm[1]
            if invert:
                value = 4095 - value
            return value
        if not 0 <= value <= 4095:
            raise ValueError("Out of range")
        if invert:
            value = 4095 - value
        if value == 0:
            self.pwm(index, 0, 4096)
        elif value == 4095:
            self.pwm(index, 4096, 0)
        else:
            self.pwm(index, 0, value)


class DCMotors:

    FORWARD = const(0)
    BACKWARD = const(1)
    LEFT = const(2)
    RIGHT = const(3)

    DC_FREQUENCE = 3000

    def __init__(self, port = None,
                 m1: tuple[int, int, int] = (
                     10, 11, 12),  # default by clockwise
                 m2: tuple[int, int, int] = (
                     15, 13, 14),  # default by clockwise
                 m3: tuple[int, int, int] = None,  # for external DC board
                 m4: tuple[int, int, int] = None   # for external DC board
                 ) -> None:
        """
        @desc: Define DCMotor module on board, 
               By default, m1, m2, m3, m4 is rotating clockwise, m1, m3 on the left and m2, m4 on the right.

        @args:
            - port: connected to BeeBrain through PORTx with x: [1-6]
            - m1: tuple (pwm, in2, in1) pca9685 pin to control motor 1
            - m2: tuple (pwm, in2, in1) pca9685 pin to control motor 2
            - m3: tuple (pwm, in2, in1) pca9685 pin to control motor 3 (for BeeMotor module only)
            - m4: tuple (pwm, in2, in1) pca9685 pin to control motor 4 (for BeeMotor module only)
        @return:
            None
        """
        if port is None:
            raise ValueError("Invalid port")
        i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)))
        self.pca9685 = PCA9685(i2c, 0x40)

        self._DC_MOTORS = (
            m1, m2) if m3 is None and m4 is None else (m1, m2, m3, m4)

    def _pin(self, pin: int, value=None) -> None:
        if value is None:
            return bool(self.pca9685.pwm(pin)[0])
        if value:
            self.pca9685.pwm(pin, 4096, 0)
        else:
            self.pca9685.pwm(pin, 0, 0)

    def _set_dc_motor_freq(self):
        self.pca9685.freq(self.DC_FREQUENCE)

    def speed(self, motor_index: int, power=None) -> None:
        """
        @desc: Set speed of motor, max speed is 50%
        @requires: BeeBrain module
        @args:
            - motor_index: index of motor [0, 1]
            - power: 0% - 100%
        @returns:
            None
        """
        self._set_dc_motor_freq()
        pwm, in2, in1 = self._DC_MOTORS[motor_index]
        if power >= 0:
            self._pin(in1, True)
            self._pin(in2, False)
        else:
            self._pin(in1, False)
            self._pin(in2, True)
            power = -power
        if power >= 50:
            power = 50
        if power < 0:
            power = 0
        power = int(power / 100.0 * 4096.0)
        if power is None:
            power = self.pca9685.duty(pwm)
            if self._pin(in2) and not self._pin(in1):
                power = -power
            return power

        self.pca9685.duty(pwm, power)

    def brake(self, motor_index: int) -> None:
        """
        @desc: Brake motor
        @requires: BeeBrain module
        @args:
            - motor_index: index of motor [0, 1]
        @returns:
            None
        """
        pwm, in2, in1 = self._DC_MOTORS[motor_index]
        self._pin(in1, False)
        self._pin(in2, False)
        self.pca9685.duty(pwm, 0)


class Servos:

    SERVO_FREQUENCE = 50
    MIN_US = 600
    MAX_US = 2400
    DEGREES = 270

    def __init__(self, port,
                 freq=50,
                 min_us=600,
                 max_us=2400,
                 degrees=DEGREES) -> None:
        """
        @desc: Define Servo module
        @requires: BeeBrain module
        @args:
            - freq: frequence
            - min_us: min pmw
            - max_us: max pmw
            - degrees: max degree
        @returns:
            None
        """
        i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)))
        self.period = 1000000 / self.SERVO_FREQUENCE
        self.min_duty = self._us2duty(self.MIN_US)
        self.max_duty = self._us2duty(self.MAX_US)
        self.degrees = degrees
        self.freq = self.SERVO_FREQUENCE
        self.pca9685 = PCA9685(i2c, 0x40)

    def _us2duty(self, value):
        return int(4095 * value / self.period)

    def _set_servo_freq(self):
        self.pca9685.freq(self.SERVO_FREQUENCE)

    def position(self, index: int, degrees=None, radians=None, us=None, duty=None) -> None:
        """
        @desc: Set position of servo
        @requires: BeeBrain module
        @args:
            - index: servo index [0-7]
            - degrees: set degree of servo [0-270]
            - radians: : None by default
            - us: None by default
            - duty: None by default
        @returns:
            None
        """
        self._set_servo_freq()
        span = self.max_duty - self.min_duty
        if degrees is not None:
            duty = self.min_duty + span * degrees / self.degrees
        elif radians is not None:
            duty = self.min_duty + span * radians / math.radians(self.degrees)
        elif us is not None:
            duty = self._us2duty(us)
        elif duty is not None:
            pass
        else:
            return self.pca9685.duty(index)
        duty = min(self.max_duty, max(self.min_duty, int(duty)))
        self.pca9685.duty(index, duty)

    def release(self, index):
        """
        @desc: Release servo motor
        @requires: BeeBrain module
        @args:
            - index: index of servo [0-7]
        @return:
            None
        """
        self.pca9685.duty(index, 0)
