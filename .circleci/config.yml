version: 2.1

jobs:
    deploy-firmware:
        docker:
            - image: cimg/base:stable
        steps:
            - checkout

            - run:
                  name: Install deps (ESP-IDF toolchain)
                  command: |
                      sudo apt update
                      sudo apt install -y git wget flex bison gperf python3 python3-pip python3-venv \
                        cmake ninja-build ccache libffi-dev libssl-dev dfu-util libusb-1.0-0 xz-utils
                      mkdir -p ~/esp

            - run:
                  name: <PERSON><PERSON> ESP-IDF v5.4.2
                  command: |
                      cd ~/esp
                      git clone --recursive https://github.com/espressif/esp-idf.git
                      cd esp-idf
                      git checkout v5.4.2
                      git submodule update --init --recursive

            - run:
                  name: Clone MicroPython v1.26.0
                  command: |
                      cd ~/esp
                      git clone https://github.com/micropython/micropython.git
                      cd micropython
                      git checkout v1.26.0
                      git submodule update --init --recursive

            - run:
                  name: Patch board config & add modules
                  command: |
                      cat > ~/esp/micropython/ports/esp32/boards/ESP32_GENERIC_S3/mpconfigboard.h \<< 'EOF'
                      #ifndef MICROPY_HW_BOARD_NAME
                      #define MICROPY_HW_BOARD_NAME "BeeBrain"
                      #endif
                      #ifndef MICROPY_HW_MCU_NAME
                      #define MICROPY_HW_MCU_NAME "ESP32S3"
                      #endif
                      EOF
                      cp ~/project/deploy/* ~/esp/micropython/ports/esp32/modules/ || true
                      ls -la ~/esp/micropython/ports/esp32/modules/

            - run:
                  name: Build MicroPython for ESP32-S3
                  command: |
                      # Export ESP-IDF (phải trong cùng step với build)
                      cd ~/esp/esp-idf
                      ./install.sh
                      source export.sh
                      export IDF_TARGET=esp32s3

                      # Build mpy-cross & ports/esp32 cho S3
                      cd ~/esp/micropython
                      make -C mpy-cross

                      cd ports/esp32
                      make submodules
                      # Nếu board có PSRAM, dùng BOARD=ESP32_GENERIC_S3_SPIRAM
                      make BOARD=ESP32_GENERIC_S3

            - run:
                  name: Store firmware artifact
                  command: |
                      mkdir -p /tmp/artifacts
                      cp ~/esp/micropython/ports/esp32/build-ESP32_GENERIC_S3/firmware.bin /tmp/artifacts/
                      mv /tmp/artifacts/firmware.bin /tmp/artifacts/BeE-board-V2-$(date +%Y%m%d)-v1.26.0.bin

            - store_artifacts:
                  path: /tmp/artifacts

workflows:
    BeeBlock:
        jobs:
            - deploy-firmware
