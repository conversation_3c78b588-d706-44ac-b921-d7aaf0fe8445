# LedMatrix - <PERSON><PERSON><PERSON> <PERSON>rậ<PERSON> LED

## Giới thiệu

Module LedMatrix cung cấp giao diện điều khiển ma trận LED 5x5 sử dụng WS2812B (NeoPixel) cho BeE Board. Module hỗ trợ hiển thị văn bản cuộn, biểu tượng cảm xúc, số, và các hiệu ứng ánh sáng đa dạng với 16.7 triệu màu.

Ma trận LED là một công cụ hiển thị mạnh mẽ và trực quan, cho phép tạo ra các thông điệp, biểu tượng và hiệu ứng ánh sáng sinh động. Với 25 LED RGB được sắp xếp thành lưới 5x5, có thể tạo ra vô số pattern và animation.

**Ứng dụng thực tế:**

-   Hi<PERSON><PERSON> thị thông báo và tin nhắn
-   Bi<PERSON><PERSON> tượng cảm xúc và trạng thái
-   <PERSON>ồ<PERSON> hồ số và hiển thị thời gian
-   <PERSON> đơn gi<PERSON>n (Snake, <PERSON><PERSON><PERSON> mini)
-   Hiệu ứng trang trí và nghệ thuật ánh sáng
-   Báo hiệu trạng thái thiết bị

## Thông số kỹ thuật

| Thông số           | Giá trị                        |
| ------------------ | ------------------------------ |
| Kích thước ma trận | 5x5 pixels (25 LEDs)           |
| Loại LED           | WS2812B (NeoPixel)             |
| Độ phân giải màu   | 24-bit RGB (16.7M màu)         |
| Điện áp hoạt động  | 5V (3.3V tương thích)          |
| Dòng tiêu thụ      | ~1.5A (full brightness)        |
| Tần số điều khiển  | 800kHz                         |
| Độ sáng tối đa     | Giới hạn 25% (tránh quá nhiệt) |
| Giao tiếp          | Single-wire serial             |
| Font chữ           | 5x5 ASCII characters           |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from LedMatrix import LedMatrix

# Khởi tạo ma trận LED với PORT1
matrix = LedMatrix(port=bee.PORT1)
```

### Các phương thức chính

#### `text(message, delay_ms)` - Hiển thị văn bản cuộn

```python
# Hiển thị text cuộn với tốc độ mặc định
matrix.text("Hello BeE!")

# Hiển thị với tốc độ tùy chỉnh
matrix.text("Fast scroll", 100)  # Cuộn nhanh
```

#### `color(h, s, v)` - Đặt màu HSV

```python
# Đặt màu đỏ
matrix.color(0, 1, 1)

# Đặt màu xanh lá với độ sáng 50%
matrix.color(120, 1, 0.5)
```

#### `color_rgb(r, g, b)` - Đặt màu RGB

```python
# Đặt màu tím
matrix.color_rgb(255, 0, 255)

# Đặt màu vàng nhạt
matrix.color_rgb(255, 255, 100)
```

#### `picture(pattern)` - Hiển thị hình ảnh

```python
# Hiển thị biểu tượng có sẵn
matrix.picture(LedMatrix.SMILE)

# Hiển thị pattern tùy chỉnh
custom_pattern = [1,0,1,0,1, 0,1,0,1,0, 1,0,1,0,1, 0,1,0,1,0, 1,0,1,0,1]
matrix.picture(custom_pattern)
```

#### `digit(number, position)` - Hiển thị số

```python
# Hiển thị số 5 ở giữa
matrix.digit(5, LedMatrix.CENTER)

# Hiển thị số 3 bên trái
matrix.digit(3, LedMatrix.LEFT)
```

### Biểu tượng có sẵn

```python
LedMatrix.SMILE    # Mặt cười
LedMatrix.NORMAL   # Mặt bình thường
LedMatrix.SAD      # Mặt buồn
LedMatrix.HEART    # Trái tim
```

## Ví dụ Blockly

```blockly
when program starts:
    set led_matrix at PORT1

    set LED matrix color to RED
    show SMILE icon on LED matrix
    wait 2 seconds

    set LED matrix color to BLUE
    scroll text "Hello World!" on LED matrix

repeat forever:
    show digit 1 on LED matrix
    wait 1 second
    show digit 2 on LED matrix
    wait 1 second
    show digit 3 on LED matrix
    wait 1 second
```

## Ví dụ Python

### Ví dụ cơ bản - Hiển thị biểu tượng cảm xúc

```python
from BeeBrain import bee
from LedMatrix import LedMatrix
import time

# Khởi tạo ma trận LED
matrix = LedMatrix(port=bee.PORT1)

# Danh sách biểu tượng và màu sắc
emotions = [
    (LedMatrix.SMILE, (60, 1, 1)),    # Vàng - vui
    (LedMatrix.NORMAL, (120, 1, 0.5)), # Xanh - bình thường
    (LedMatrix.SAD, (240, 1, 0.8)),    # Xanh dương - buồn
    (LedMatrix.HEART, (0, 1, 0.6))     # Đỏ - yêu
]

def emotion_display():
    """Hiển thị các biểu tượng cảm xúc"""
    while True:
        for emotion, color in emotions:
            # Đặt màu
            matrix.color(color[0], color[1], color[2])

            # Hiển thị biểu tượng
            matrix.picture(emotion)

            # Chờ 2 giây
            time.sleep(2)

            # Tắt màn hình
            matrix.off()
            time.sleep(0.5)

# Chạy demo
emotion_display()
```

### Ví dụ nâng cao - Đồng hồ số với hiệu ứng

```python
from BeeBrain import bee
from LedMatrix import LedMatrix
import time
import math

# Khởi tạo ma trận LED
matrix = LedMatrix(port=bee.PORT1)

class DigitalClock:
    def __init__(self):
        self.rainbow_offset = 0

    def get_rainbow_color(self, offset=0):
        """Tạo màu cầu vồng thay đổi theo thời gian"""
        hue = (self.rainbow_offset + offset) % 360
        return (hue, 1, 0.8)

    def display_time_digits(self):
        """Hiển thị giờ:phút bằng số"""
        current_time = time.localtime()
        hour = current_time[3]
        minute = current_time[4]

        # Hiển thị giờ (2 số cuối)
        hour_tens = hour // 10
        hour_ones = hour % 10

        # Hiển thị phút (2 số cuối)
        minute_tens = minute // 10
        minute_ones = minute % 10

        # Sequence hiển thị: giờ chục -> giờ đơn vị -> phút chục -> phút đơn vị
        digits = [hour_tens, hour_ones, minute_tens, minute_ones]
        positions = [LedMatrix.LEFT, LedMatrix.RIGHT, LedMatrix.LEFT, LedMatrix.RIGHT]
        colors = [
            (0, 1, 0.8),    # Đỏ cho giờ chục
            (30, 1, 0.8),   # Cam cho giờ đơn vị
            (120, 1, 0.8),  # Xanh cho phút chục
            (240, 1, 0.8)   # Xanh dương cho phút đơn vị
        ]

        for i, (digit, pos, color) in enumerate(zip(digits, positions, colors)):
            matrix.color(color[0], color[1], color[2])
            matrix.digit(digit, pos)
            time.sleep(1.5)

            # Hiệu ứng fade out
            for brightness in range(8, 0, -1):
                matrix.color(color[0], color[1], brightness/10)
                matrix.digit(digit, pos)
                time.sleep(0.05)

            matrix.off()
            time.sleep(0.2)

    def scrolling_time(self):
        """Hiển thị thời gian cuộn"""
        current_time = time.localtime()
        time_str = f"{current_time[3]:02d}:{current_time[4]:02d}:{current_time[5]:02d}"

        # Màu thay đổi theo giây
        hue = (current_time[5] * 6) % 360  # Mỗi giây đổi màu
        matrix.color(hue, 1, 0.6)

        matrix.text(time_str, 200)

    def breathing_heart(self):
        """Hiệu ứng trái tim thở"""
        for brightness in range(1, 11):
            matrix.color(0, 1, brightness/10)  # Đỏ với độ sáng thay đổi
            matrix.picture(LedMatrix.HEART)
            time.sleep(0.1)

        for brightness in range(10, 0, -1):
            matrix.color(0, 1, brightness/10)
            matrix.picture(LedMatrix.HEART)
            time.sleep(0.1)

    def run_clock(self):
        """Chạy đồng hồ với nhiều chế độ"""
        mode = 0
        last_minute = -1

        while True:
            current_time = time.localtime()
            current_minute = current_time[4]

            # Chuyển chế độ mỗi phút
            if current_minute != last_minute:
                mode = (mode + 1) % 3
                last_minute = current_minute

                # Hiệu ứng chuyển đổi
                matrix.color_rgb(255, 255, 255)
                matrix.text("MODE", 150)
                time.sleep(1)

            if mode == 0:
                # Chế độ số
                self.display_time_digits()
            elif mode == 1:
                # Chế độ cuộn
                self.scrolling_time()
            else:
                # Chế độ trái tim (mỗi giây)
                for _ in range(3):
                    self.breathing_heart()

            self.rainbow_offset = (self.rainbow_offset + 10) % 360

def create_custom_patterns():
    """Tạo các pattern tùy chỉnh"""

    # Pattern mũi tên
    arrow_right = [
        0,0,1,0,0,
        0,0,0,1,0,
        1,1,1,1,1,
        0,0,0,1,0,
        0,0,1,0,0
    ]

    # Pattern X
    x_pattern = [
        1,0,0,0,1,
        0,1,0,1,0,
        0,0,1,0,0,
        0,1,0,1,0,
        1,0,0,0,1
    ]

    # Pattern checkerboard
    checker = [
        1,0,1,0,1,
        0,1,0,1,0,
        1,0,1,0,1,
        0,1,0,1,0,
        1,0,1,0,1
    ]

    patterns = [arrow_right, x_pattern, checker]
    colors = [(0,1,0.8), (120,1,0.8), (240,1,0.8)]

    for pattern, color in zip(patterns, colors):
        matrix.color(color[0], color[1], color[2])
        matrix.picture(pattern)
        time.sleep(2)
        matrix.off()
        time.sleep(0.5)

def animation_demo():
    """Demo các hiệu ứng animation"""

    # Hiệu ứng sóng
    for wave in range(10):
        for x in range(5):
            pattern = [0] * 25
            for y in range(5):
                if (x + wave) % 3 == y % 3:
                    pattern[x + y * 5] = 1

            hue = (wave * 36) % 360
            matrix.color(hue, 1, 0.5)
            matrix.picture(pattern)
            time.sleep(0.2)

    # Hiệu ứng xoay
    for rotation in range(8):
        pattern = [0] * 25
        # Tạo pattern xoay
        center_x, center_y = 2, 2
        for r in range(1, 3):
            angle = rotation * 45 * math.pi / 180
            x = int(center_x + r * math.cos(angle))
            y = int(center_y + r * math.sin(angle))
            if 0 <= x < 5 and 0 <= y < 5:
                pattern[x + y * 5] = 1

        matrix.color(rotation * 45, 1, 0.7)
        matrix.picture(pattern)
        time.sleep(0.3)

# Chạy chương trình
if __name__ == "__main__":
    print("=== LedMatrix Demo ===")

    # Demo patterns
    create_custom_patterns()

    # Demo animation
    animation_demo()

    # Chạy đồng hồ
    clock = DigitalClock()
    clock.run_clock()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Emotion display**: Hiển thị các biểu tượng cảm xúc với màu sắc tương ứng
2. **Color setting**: Sử dụng HSV color model để dễ dàng thay đổi màu
3. **Timing**: Điều khiển thời gian hiển thị và chuyển đổi

### Ví dụ nâng cao:

1. **Digital clock**: Tạo đồng hồ số với nhiều chế độ hiển thị
2. **Color effects**: Sử dụng màu cầu vồng và hiệu ứng breathing
3. **Pattern generation**: Tạo các pattern tùy chỉnh và animation
4. **Mode switching**: Chuyển đổi giữa các chế độ hiển thị khác nhau

## Bài tập mở rộng

1. **Game Snake mini**: Tạo trò chơi rắn săn mồi trên ma trận 5x5
2. **Visualizer âm nhạc**: Hiển thị spectrum âm thanh bằng LED matrix
3. **Thông báo thông minh**: Hệ thống hiển thị thông báo từ smartphone qua Bluetooth

## Lỗi thường gặp

```{admonition} Lỗi: LED không sáng hoặc màu sai
:class: warning

**Nguyên nhân**: Nguồn điện không đủ hoặc kết nối data sai

**Giải pháp**:
- Đảm bảo nguồn 5V đủ mạnh (>2A)
- Kiểm tra kết nối data pin
- Thêm tụ lọc 1000µF gần nguồn
- Sử dụng dây nguồn đủ dày
```

```{admonition} Lỗi: Hiển thị text bị lỗi font
:class: warning

**Nguyên nhân**: Ký tự không được hỗ trợ hoặc encoding sai

**Giải pháp**:
- Chỉ sử dụng ASCII characters (32-127)
- Tránh ký tự đặc biệt và Unicode
- Kiểm tra font definition trong code
- Sử dụng ký tự thay thế cho các ký tự không hỗ trợ
```

```{admonition} Lỗi: Ma trận quá nóng
:class: warning

**Nguyên nhân**: Độ sáng quá cao hoặc hiển thị màu trắng liên tục

**Giải pháp**:
- Giảm độ sáng xuống <50%
- Tránh hiển thị màu trắng full brightness
- Thêm tản nhiệt nếu cần
- Sử dụng duty cycle để giảm nhiệt
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [WS2812B LED Matrix Guide](https://learn.adafruit.com/adafruit-neopixel-uberguide)
-   [5x5 Font Design](https://fontstruct.com/gallery/tag/5x5)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
