# Tài Liệu BeE Board - Thư Viện Module Python/MicroPython

Chào mừng bạn đến với bộ tài liệu đầy đủ cho các module phần cứng của BeE Board! Đây là tài nguyên học tập dành cho học sinh, gi<PERSON><PERSON> viên và những người mới bắt đầu với lập trình nhúng và robotics.

## Giới thiệu BeE Board

BeE Board là một kit robotics STEM được thiết kế đặc biệt cho gi<PERSON>o dục, gi<PERSON><PERSON> học sinh dễ dàng tiếp cận với lập trình nhúng, IoT và robotics thông qua:

-   **Giao diện thân thiện**: Hỗ trợ cả lập trình kéo thả (BeE IDE) và lập trình code (Python)
-   **Kết hợp Lego Technic**: Dễ dàng xây dựng robot và mô hình với Lego Technic
-   **Module đa dạng**: <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> c<PERSON>, LED, m<PERSON><PERSON> hình, âm thanh
-   **Dự án thực tế**: Từ cơ bản đến nâng cao
-   **Cộng đồng hỗ trợ**: Tài liệu chi tiết và ví dụ phong phú

## Danh Sách Module

### 🧠 [BeeBrain - Module Điều Khiển Chính](bee-brain.md)

Module điều khiển trung tâm tích hợp tất cả chức năng

-   Điều khiển robot, kết nối WiFi/Bluetooth
-   GPIO, analog, PWM tích hợp
-   Ví dụ: Robot IoT thông minh, hệ thống tự động

### 🎮 Module Điều Khiển Cơ Bản

#### 🔘 [Button - Module Nút Bấm](button.md)

Điều khiển nút bấm với chống nhiễu và xử lý sự kiện

-   Phát hiện nhấn, thả, nhấn giữ
-   Callback functions
-   Ví dụ: Điều khiển LED, menu điều hướng

#### ⚡ [GPIO - Module GPIO Đơn Giản](gpio.md)

Điều khiển GPIO cơ bản cho LED, relay, buzzer

-   Bật/tắt digital output
-   Đọc trạng thái pin
-   Ví dụ: LED nhấp nháy, điều khiển relay

#### 🌈 [Neopixel - Module LED RGB](neopixel.md)

Điều khiển LED RGB WS2812B với 16.7 triệu màu

-   Đặt màu RGB và Hex
-   Hiệu ứng ánh sáng
-   Ví dụ: Cầu vồng chạy, hiệu ứng thở

### 📺 Module Hiển Thị

#### 📺 [Oled - Module Màn Hình OLED](oled.md)

Hiển thị văn bản và đồ họa trên màn hình OLED 128x64

-   Hiển thị text, hình ảnh, biểu tượng
-   Vẽ đường, hình tròn, hình chữ nhật
-   Ví dụ: Đồng hồ số, hiển thị cảm biến

#### 🔲 [LedMatrix - Module Ma Trận LED](led-matrix.md)

Hiển thị văn bản, số và biểu tượng trên ma trận LED 5x5

-   Text cuộn, biểu tượng cảm xúc
-   Hiệu ứng màu sắc
-   Ví dụ: Đồng hồ LED, game đơn giản

#### 🔢 [LedSegment - Module LED 7 Đoạn](led-segment.md)

Hiển thị số và text trên LED 7 đoạn 4 chữ số

-   Hiển thị số, hex, nhiệt độ
-   Text cuộn, điều chỉnh độ sáng
-   Ví dụ: Đồng hồ số, bảng điểm

### 🔊 Module Âm Thanh

#### 🔊 [Buzzer - Module Âm Thanh](buzzer.md)

Tạo âm thanh và phát nhạc với PWM

-   Phát tần số và nốt nhạc
-   Chuỗi giai điệu
-   Ví dụ: Chuông cửa, hệ thống báo động

### 📡 Module Cảm Biến

#### 📏 [Ultrasonic - Module Cảm Biến Siêu Âm](ultrasonic.md)

Đo khoảng cách chính xác từ 2cm đến 4m

-   Đo khoảng cách mm/cm
-   Phát hiện vật thể
-   Ví dụ: Robot tránh vật cản, cảnh báo xâm nhập

#### 🎯 [Mpu6050 - Module Cảm Biến IMU](mpu6050.md)

Cảm biến gia tốc và con quay 6 trục

-   Đo gia tốc, góc quay, nhiệt độ
-   Tính góc nghiêng (pitch, roll, yaw)
-   Ví dụ: Robot cân bằng, phát hiện rung lắc

#### 🌈 [ColorDetect - Module Cảm Biến Màu](color-detect.md)

Nhận diện màu sắc RGB với độ chính xác cao

-   Đọc giá trị RGB, HSV
-   Nhận diện màu cơ bản
-   Ví dụ: Robot phân loại màu, game tương tác

#### 🛤️ [LineDetect - Module Cảm Biến Dò Đường](line-detect.md)

Cảm biến hồng ngoại 4 kênh cho robot theo đường

-   Phát hiện vạch đen/trắng
-   Thuật toán PID cho chuyển động mượt
-   Ví dụ: Robot theo đường, xe AGV

### ⚙️ Module Động Cơ

#### ⚙️ [Motor - Module Điều Khiển Động Cơ](motor.md)

Điều khiển động cơ DC và servo motor với PCA9685

-   2 động cơ DC + 8 servo
-   Điều khiển tốc độ và vị trí
-   Ví dụ: Robot di chuyển, cánh tay robot

#### 🔧 [MotorOnBoard - Module Motor Tích Hợp](motor-onboard.md)

Motor DC và servo được tích hợp trực tiếp trên board

-   2 motor DC + 4 servo tích hợp
-   Điều khiển trực tiếp từ ESP32
-   Ví dụ: Robot tự động, hệ thống automation

### 🔐 Module Nhận Diện

#### 📱 [RC522 - Module Đọc Thẻ RFID](rc522.md)

Đọc thẻ RFID/NFC với quản lý danh sách

-   Hỗ trợ MIFARE Classic, NTAG
-   Quản lý danh sách thẻ JSON
-   Ví dụ: Kiểm soát truy cập, điểm danh tự động

### 🎮 Module Điều Khiển Từ Xa

#### 🎮 [Gamepad - Module Gamepad MQTT](gamepad.md)

Điều khiển từ xa qua WiFi/MQTT với gamepad hoặc smartphone

-   Kết nối MQTT server, nhận lệnh JSON
-   Hỗ trợ nhiều người chơi cùng lúc
-   Ví dụ: Robot điều khiển từ xa, game nhiều người

## Cấu Trúc Tài Liệu

Mỗi module được tài liệu hóa theo cấu trúc chuẩn:

### 1. 📖 Giới thiệu

-   Mô tả chức năng và ứng dụng
-   Nguyên lý hoạt động cơ bản

### 2. 📊 Thông số kỹ thuật

-   Bảng thông số chi tiết
-   Điện áp, dòng điện, phạm vi hoạt động

### 3. 💻 Giao diện lập trình

-   API functions và parameters
-   Ví dụ code ngắn gọn

### 4. 🧩 Ví dụ Blockly

-   Code dạng block cho người mới bắt đầu
-   Dễ hiểu và trực quan

### 5. 🐍 Ví dụ Python

-   **Cơ bản**: Dễ hiểu, phù hợp học sinh
-   **Nâng cao**: Ứng dụng thực tế phức tạp

### 6. 📝 Giải thích mã

-   Từng dòng code được giải thích
-   Logic và nguyên lý hoạt động

### 7. 🎯 Bài tập mở rộng

-   Gợi ý dự án sáng tạo
-   Thử thách cho học sinh

### 8. ⚠️ Lỗi thường gặp

-   Các lỗi phổ biến và cách khắc phục
-   Tips và tricks

### 9. 📚 Tài nguyên tham khảo

-   Links hữu ích và tài liệu bổ sung

## Hướng Dẫn Sử Dụng

### Cho Học Sinh

1. **Bắt đầu với Blockly**: Dễ hiểu, không cần kiến thức lập trình
2. **Chuyển sang Python**: Khi đã quen với logic cơ bản
3. **Thực hành ví dụ**: Từ cơ bản đến nâng cao
4. **Tự tạo dự án**: Kết hợp nhiều module

### Cho Giáo Viên

1. **Chuẩn bị bài giảng**: Sử dụng ví dụ có sẵn
2. **Demo trực tiếp**: Code mẫu đã test kỹ
3. **Giao bài tập**: Sử dụng phần "Bài tập mở rộng"
4. **Hỗ trợ debug**: Tham khảo "Lỗi thường gặp"

## Dự Án Tích Hợp

### 🤖 Robot Thông Minh

Kết hợp: Motor + Ultrasonic + LED + Buzzer

```python
# Robot tránh vật cản với âm thanh và ánh sáng cảnh báo
```

### 🏠 Nhà Thông Minh Mini

Kết hợp: Button + OLED + Neopixel + Buzzer

```python
# Hệ thống điều khiển ánh sáng và cảnh báo
```

### 🎮 Console Game

Kết hợp: Button + LED Matrix + Buzzer

```python
# Game Snake hoặc Tetris mini
```

### 📊 Trạm Thời Tiết

Kết hợp: OLED + Sensors + LED

```python
# Hiển thị dữ liệu môi trường real-time
```

## Yêu Cầu Hệ Thống

### Phần Cứng

-   **BeE Board**: ESP32 S3-based development board
-   **Modules**: Theo từng dự án cụ thể
-   **Nguồn**: 5V/2A
    -   USB-C
    -   Pin Lithium Polymer (2 x pin 14500 1500mAh)
    -   Nguồn DC 7.2-8.4V jack PH2.0
-   **Cáp**: USB-C cho lập trình

### Phần Mềm

-   **BeE IDE**: [beeblock.vn/studio/bee-ide](https://beeblock.vn/studio/bee-ide)
-   **MicroPython**: Đã tích hợp sẵn
-   **Thư viện**: Auto-import trong Firmware và IDE

## Hỗ Trợ và Cộng Đồng

### 📞 Liên Hệ Hỗ Trợ

-   **Website**: [beeblock.vn](https://beeblock.vn)
-   **Email**: <EMAIL>
-   **Facebook**: [fb.com/beeblock.vn](https://facebook.com/beeblock.vn)

### 📖 Tài Nguyên Bổ Sung

-   **Video Tutorials**: Hướng dẫn từng bước
-   **Project Gallery**: Dự án từ cộng đồng
-   **Code Repository**: GitHub với examples

## Đóng Góp

Chúng tôi luôn chào đón sự đóng góp từ cộng đồng thông qua [Email](mailto:<EMAIL>) hoặc [Liên hệ](https://beeblock.vn/lien-he)

## Giấy Phép

Dự án này được phát hành dưới giấy phép Private License – All Rights Reserved. Mọi nội dung đều được bảo vệ bản quyền. Nghiêm cấm sao chép, tái bản hoặc sử dụng bất kỳ phần nào của tài liệu này mà không có sự cho phép bằng văn bản của BeE STEM Solutions.

---

**Chúc bạn học tập và sáng tạo vui vẻ với BeE Board! 🐝**

_Cập nhật lần cuối: Tháng 10, 2024_
