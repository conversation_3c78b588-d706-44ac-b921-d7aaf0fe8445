# Neopixel - <PERSON><PERSON>le LED RGB

## Giới thiệu

Module Neopixel cung cấp giao diện điều khiển LED RGB WS2812B (NeoPixel) cho BeE Board. Module hỗ trợ điều khiển từng LED riêng lẻ với màu sắc RGB hoặc hex, tạo ra các hiệu ứng ánh sáng đa dạng và sinh động.

NeoPixel là dải LED RGB thông minh, mỗi LED có thể hiển thị 16.7 triệu màu khác nhau. Trên BeE Board có 2 LED NeoPixel tích hợp sẵn và có thể mở rộng thêm bằng cách kết nối dải LED NeoPixel ngoài.

**Ứng dụng thực tế:**

-   Hiệu ứng ánh sáng trang trí
-   Báo hiệu trạng thái hệ thống
-   Hiển thị dữ liệu bằng màu sắc
-   <PERSON><PERSON><PERSON> đè<PERSON> LED thông minh
-   <PERSON><PERSON><PERSON> ứng ánh sáng cho robot

## Thông số kỹ thuật

| Thông số           | Giá trị                 |
| ------------------ | ----------------------- |
| Loại LED           | WS2812B (NeoPixel)      |
| Điện áp hoạt động  | 5V (3.3V tương thích)   |
| Dòng điện mỗi LED  | ~60mA (full brightness) |
| Số màu hỗ trợ      | 16.7 triệu (24-bit RGB) |
| Tần số điều khiển  | 800kHz                  |
| Giao tiếp          | Single-wire serial      |
| Độ sáng tối đa     | 255 (mỗi kênh RGB)      |
| Nhiệt độ hoạt động | -25°C đến +85°C         |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from Neopixel import Neopixel

# LED tích hợp trên board (led1)
neo_onboard = bee.led1

# Dải LED ngoài (8 LEDs)
neo_strip = Neopixel(port=bee.PORT1, number_of_led=8)
```

### Các phương thức chính

#### `set_rgb(led_id, r, g, b)` - Đặt màu RGB

```python
# Đặt LED đầu tiên màu đỏ
neo.set_rgb(0, 255, 0, 0)

# Đặt LED thứ hai màu xanh lá
neo.set_rgb(1, 0, 255, 0)
```

#### `set_hex(led_id, hex_color)` - Đặt màu bằng mã hex

```python
# Đặt LED màu tím
neo.set_hex(0, '#FF00FF')

# Đặt LED màu vàng
neo.set_hex(1, '#FFFF00')
```

#### `off(led_id)` - Tắt LED

```python
# Tắt LED đầu tiên
neo.off(0)

# Tắt tất cả LED
for i in range(neo.number_of_led):
    neo.off(i)
```

## Ví dụ Blockly

```blockly
when program starts:
    set NeoPixel LED 0 to color RED
    set NeoPixel LED 1 to color BLUE
    wait 1 second

repeat forever:
    set NeoPixel LED 0 to random color
    set NeoPixel LED 1 to random color
    wait 0.5 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Hiệu ứng nhấp nháy

```python
from BeeBrain import bee
from Neopixel import Neopixel
import time

# Khởi tạo NeoPixel
neo = Neopixel(port=bee.PORT1, number_of_led=2)

# Màu sắc cơ bản
colors = [
    (255, 0, 0),    # Đỏ
    (0, 255, 0),    # Xanh lá
    (0, 0, 255),    # Xanh dương
    (255, 255, 0),  # Vàng
    (255, 0, 255),  # Tím
    (0, 255, 255),  # Cyan
]

# Hiệu ứng nhấp nháy
while True:
    for color in colors:
        # Đặt cả 2 LED cùng màu
        neo.set_rgb(0, color[0], color[1], color[2])
        neo.set_rgb(1, color[0], color[1], color[2])
        time.sleep(0.5)

        # Tắt LED
        neo.off(0)
        neo.off(1)
        time.sleep(0.2)
```

### Ví dụ nâng cao - Hiệu ứng cầu vồng chạy

```python
from BeeBrain import bee
from Neopixel import Neopixel
import time
import math

# Khởi tạo dải LED 8 bóng
neo = Neopixel(port=bee.PORT1, number_of_led=8)

def hsv_to_rgb(h, s, v):
    """Chuyển đổi HSV sang RGB"""
    h = h % 360
    c = v * s
    x = c * (1 - abs((h / 60) % 2 - 1))
    m = v - c

    if 0 <= h < 60:
        r, g, b = c, x, 0
    elif 60 <= h < 120:
        r, g, b = x, c, 0
    elif 120 <= h < 180:
        r, g, b = 0, c, x
    elif 180 <= h < 240:
        r, g, b = 0, x, c
    elif 240 <= h < 300:
        r, g, b = x, 0, c
    else:
        r, g, b = c, 0, x

    return int((r + m) * 255), int((g + m) * 255), int((b + m) * 255)

def rainbow_cycle(wait_ms=50):
    """Hiệu ứng cầu vồng chạy"""
    for cycle in range(256):
        for i in range(neo.number_of_led):
            # Tính toán màu cho mỗi LED
            hue = (cycle + i * 256 // neo.number_of_led) % 256
            hue = hue * 360 // 256  # Chuyển sang độ

            r, g, b = hsv_to_rgb(hue, 1.0, 0.5)  # Độ sáng 50%
            neo.set_rgb(i, r, g, b)

        time.sleep_ms(wait_ms)

def breathing_effect(color, duration_ms=2000):
    """Hiệu ứng thở với màu cố định"""
    steps = 50
    for step in range(steps):
        # Tính độ sáng theo hàm sin
        brightness = (math.sin(step * 2 * math.pi / steps) + 1) / 2

        r = int(color[0] * brightness)
        g = int(color[1] * brightness)
        b = int(color[2] * brightness)

        # Áp dụng cho tất cả LED
        for i in range(neo.number_of_led):
            neo.set_rgb(i, r, g, b)

        time.sleep_ms(duration_ms // steps)

# Chạy hiệu ứng
while True:
    # Hiệu ứng cầu vồng 5 giây
    for _ in range(100):
        rainbow_cycle(50)

    # Hiệu ứng thở màu đỏ 3 giây
    for _ in range(3):
        breathing_effect((255, 0, 0), 1000)

    # Hiệu ứng thở màu xanh 3 giây
    for _ in range(3):
        breathing_effect((0, 255, 0), 1000)
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Khởi tạo**: Tạo đối tượng Neopixel với pin và số lượng LED
2. **Mảng màu**: Định nghĩa danh sách các màu RGB cơ bản
3. **Vòng lặp hiệu ứng**: Lặp qua từng màu, đặt cho LED, chờ, rồi tắt

### Ví dụ nâng cao:

1. **Chuyển đổi màu**: Hàm `hsv_to_rgb()` chuyển HSV sang RGB để tạo cầu vồng
2. **Hiệu ứng cầu vồng**: Tính toán màu cho từng LED dựa trên vị trí và thời gian
3. **Hiệu ứng thở**: Sử dụng hàm sin để tạo độ sáng thay đổi mượt mà
4. **Kết hợp hiệu ứng**: Chuyển đổi giữa các hiệu ứng khác nhau

## Bài tập mở rộng

1. **Đèn báo nhiệt độ**: Tạo hệ thống LED thay đổi màu theo nhiệt độ (xanh=lạnh, đỏ=nóng)
2. **Visualizer âm thanh**: Kết hợp với microphone để tạo hiệu ứng LED theo nhạc
3. **Đồng hồ LED**: Hiển thị giờ bằng màu sắc khác nhau cho giờ, phút, giây

## Lỗi thường gặp

```{admonition} Lỗi: LED không sáng
:class: warning

**Nguyên nhân**: Nguồn điện không đủ hoặc kết nối sai

**Giải pháp**:
- Kiểm tra nguồn 5V cho dải LED ngoài
- Đảm bảo mass chung giữa BeE Board và LED strip
- Kiểm tra pin data connection
- Thử với số LED ít hơn
```

```{admonition} Lỗi: Màu sắc không đúng
:class: warning

**Nguyên nhân**: Giá trị RGB ngoài phạm vi hoặc thứ tự màu sai

**Giải pháp**:
- Đảm bảo giá trị RGB trong khoảng 0-255
- Một số LED có thứ tự GRB thay vì RGB
- Kiểm tra datasheet của LED strip
```

```{admonition} Lỗi: LED nhấp nháy không đều
:class: warning

**Nguyên nhân**: Timing không chính xác hoặc nhiễu tín hiệu

**Giải pháp**:
- Sử dụng dây data ngắn và chất lượng tốt
- Thêm điện trở 330Ω nối tiếp với data pin
- Tránh interrupt trong khi gửi dữ liệu
- Sử dụng tụ lọc nguồn
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [WS2812B Datasheet](https://cdn-shop.adafruit.com/datasheets/WS2812B.pdf)
-   [NeoPixel Guide](https://learn.adafruit.com/adafruit-neopixel-uberguide)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
