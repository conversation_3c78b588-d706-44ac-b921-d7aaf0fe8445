# Gamepad - Mo<PERSON>le Đ<PERSON> Khiển Gamepad MQTT

## Giới thiệu

Gamepad là module điều khiển từ xa thông qua giao thức MQTT, cho phép điều khiển BeE Board bằng gamepad hoặc ứng dụng di động qua mạng WiFi. Module kết nối đến server MQTT và nhận lệnh điều khiển dưới dạng JSON, hỗ trợ các nút bấm và lệnh tùy chỉnh.

Module được thiết kế để tạo ra trải nghiệm điều khiển từ xa mượt mà cho robot, xe điều khiển và các dự án IoT. Đặc biệt hữu ích cho game, robot điều khiển từ xa và hệ thống automation thông minh.

**Ứng dụng thực tế:**

-   Robot điều khiển từ xa qua WiFi
-   Xe <PERSON> thông minh với camera
-   Game điều khiển nhiều người chơi
-   <PERSON>ệ thống điều khiển thiết bị gia đình
-   Drone điều khiển bằng smartphone
-   Robot giáo dục điều khiển từ xa
-   Hệ thống automation IoT

## Thông số kỹ thuật

| Thông số           | Giá trị                |
| ------------------ | ---------------------- |
| Giao thức          | MQTT over WiFi         |
| Message format     | JSON                   |
| Latency            | 50-200ms (tùy mạng)    |
| Max connections    | 100 (per server)       |
| Supported commands | Tất cả string commands |
| Queue size         | Unlimited              |
| Auto-reconnect     | Có                     |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee

# Khởi tạo với server mặc định, ip được hiển thị trên màn hình Oled BeE board khi kết nối WiFi
wlan = bee.connect_wifi('my_wifi', 'my_pass')
if wlan.isconnected():
    gamepad = bee.connect_gamepad(wlan.ifconfig()[0])
```

### Nhận lệnh điều khiển

#### `check_msg()` - Kiểm tra tin nhắn mới

```python
# Kiểm tra và xử lý tin nhắn MQTT
gamepad.check_msg()
```

#### `read_command()` - Đọc lệnh từ queue

```python
# Đọc lệnh tiếp theo từ queue
command = gamepad.read_command()

if command:
    print(f"Received: {command}")

    if command == "FORWARD":
        # Di chuyển tiến
        bee.move_forward()
    elif command == "BACKWARD":
        # Di chuyển lùi
        bee.move_backward()
```

#### `get_last_command()` - Lấy lệnh cuối cùng

```python
# Lấy lệnh cuối cùng nhận được
last_cmd = gamepad.get_last_command()
print(f"Last command: {last_cmd}")
```

### Kiểm tra trạng thái nút

#### `is_button_pressed(button)` - Kiểm tra nút được nhấn

```python
# Kiểm tra nút cụ thể
if gamepad.is_button_pressed("A"):
    print("Button A is pressed!")

if gamepad.is_button_pressed("START"):
    print("Start button pressed!")
```

#### `clear_button_state(button)` - Xóa trạng thái nút

```python
# Xóa trạng thái nút sau khi xử lý
gamepad.clear_button_state("A")
```

#### `clear_all_states()` - Xóa tất cả trạng thái

```python
# Xóa tất cả trạng thái nút và queue
gamepad.clear_all_states()
```

## Ví dụ Blockly

```blockly
when program starts:
    connect to WiFi "my_wifi" with password "my_pass"

    if WiFi connected:
        setup gamepad MQTT

        forever:
            check gamepad messages

            set command to read gamepad command

            if command equals "FORWARD":
                move robot forward
                set LED to GREEN
            else if command equals "BACKWARD":
                move robot backward
                set LED to RED
            else if command equals "LEFT":
                turn robot left
                set LED to BLUE
            else if command equals "RIGHT":
                turn robot right
                set LED to YELLOW
            else if command equals "STOP":
                stop robot
                set LED to WHITE

            wait 0.05 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Robot điều khiển từ xa

```python
from BeeBrain import bee
import time

def setup():
    """Khởi tạo gamepad MQTT"""
    global gamepad

    try:
        # Kết nối WiFi trước
        wlan = bee.connect_wifi("YourWiFi", "password")
        if not bee.is_wifi_connected():
            return False

        # Khởi tạo gamepad
        gamepad = bee.connect_gamepad(wlan.ifconfig()[0])

        bee.oled.clear()
        bee.oled.write("Gamepad Ready!", 15, 0)
        bee.oled.show()

        # LED sẵn sàng
        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
        bee.buzzer.play_song("C4 E4 G4")

        time.sleep(3)
        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("Setup Error!", 15, 10)
        bee.oled.write(str(e)[:20], 5, 25)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
        return False

def simple_remote_control():
    """Điều khiển robot đơn giản"""
    last_command = ""
    command_count = 0

    while True:
        try:
            # Kiểm tra tin nhắn mới
            gamepad.check_msg()

            # Đọc lệnh
            if gamepad.is_button_pressed("UP"):
                bee.move_forward()
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
                bee.buzzer.play_tone("C4", 0.1)

            elif gamepad.is_button_pressed("DOWN"):
                bee.move_backward()
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                bee.buzzer.play_tone("D4", 0.1)

            elif gamepad.is_button_pressed("LEFT"):
                bee.turn_left()
                bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)
                bee.buzzer.play_tone("E4", 0.1)

            elif gamepad.is_button_pressed("RIGHT"):
                bee.turn_right()
                bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)
                bee.buzzer.play_tone("F4", 0.1)

            elif gamepad.is_button_pressed("X"):
                bee.stop_robot()
                bee.neopixel.set_rgb(bee.LED1, 255, 255, 255)
                bee.buzzer.play_tone("G4", 0.1)

            # Kiểm tra nút thoát
            if bee.buttonB.is_pressed():
                bee.stop_robot()
                break

            time.sleep(0.05)  # 20Hz update rate

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Control Error!", 15, 10)
            bee.oled.write(str(e)[:20], 5, 25)
            bee.oled.show()
            time.sleep(2)

def button_state_demo():
    """Demo kiểm tra trạng thái nút"""
    button_list = ["A", "B", "X", "Y", "START", "SELECT", "L1", "R1"]

    while True:
        try:
            gamepad.check_msg()

            # Kiểm tra từng nút
            pressed_buttons = []
            for button in button_list:
                if gamepad.is_button_pressed(button):
                    pressed_buttons.append(button)

            # Hiển thị trạng thái
            bee.oled.clear()
            bee.oled.write("Button States", 15, 0)

            if pressed_buttons:
                bee.oled.write("Pressed:", 5, 15)
                buttons_text = ", ".join(pressed_buttons[:3])  # Hiển thị tối đa 3 nút
                bee.oled.write(buttons_text, 5, 30)

                # LED phản hồi
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)

                # Xóa trạng thái sau khi hiển thị
                for button in pressed_buttons:
                    gamepad.clear_button_state(button)
            else:
                bee.oled.write("No buttons", 15, 20)
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

            bee.oled.write("B: Exit", 20, 45)

            if bee.buttonB.is_pressed():
                break

            time.sleep(0.1)

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Button Error!", 15, 10)
            time.sleep(1)

# Chạy chương trình
if setup():
    print("1. Simple Remote Control")
    print("2. Button State Demo")

    # Demo tự động
    simple_remote_control()
    time.sleep(1)
    button_state_demo()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **MQTT connection**: Kết nối đến server MQTT và subscribe topic
2. **Command processing**: Xử lý lệnh JSON từ gamepad
3. **Robot control**: Điều khiển robot dựa trên lệnh nhận được
4. **Visual feedback**: LED và OLED phản hồi trạng thái

## Bài tập mở rộng

1. **Racing game**: Game đua xe điều khiển từ xa nhiều người chơi
2. **Treasure hunt**: Trò chơi tìm kho báu với gợi ý từ gamepad
3. **Robot soccer**: Bóng đá robot điều khiển qua MQTT

## Lỗi thường gặp

```{admonition} Lỗi: MQTT connection failed
:class: warning

**Nguyên nhân**: Không kết nối được đến server MQTT

**Giải pháp**:
- Kiểm tra kết nối WiFi trước khi khởi tạo gamepad
- Xác nhận server MQTT đang hoạt động
- Kiểm tra username/password chính xác
- Thử server MQTT khác (test.mosquitto.org)
```

```{admonition} Lỗi: Commands not received
:class: warning

**Nguyên nhân**: Topic MQTT sai hoặc JSON format không đúng

**Giải pháp**:
- Xác nhận topic format: bee/gamepad/commands/{device_id}
- Kiểm tra JSON message format: {"command": "FORWARD"}
- Sử dụng MQTT client để test publish message
- Gọi check_msg() thường xuyên trong loop
```

```{admonition} Lỗi: High latency/lag
:class: warning

**Nguyên nhân**: Mạng WiFi chậm hoặc server MQTT xa

**Giải pháp**:
- Sử dụng server MQTT gần hơn
- Giảm tần suất gửi lệnh (không spam)
- Tối ưu hóa JSON message (ngắn gọn)
- Kiểm tra chất lượng mạng WiFi
```

## Tài nguyên tham khảo

-   [MQTT Protocol Guide](https://mqtt.org/mqtt-specification/)
-   [JSON Format Reference](https://www.json.org/json-en.html)
-   [WiFi Connection Tutorial](https://docs.micropython.org/en/latest/esp32/tutorial/network_basics.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
