# Motor - Module Điều Khiển Động Cơ

## Giới thiệu

Module Motor cung cấp giao diện điều khiển động cơ DC và servo cho BeE Board thông qua chip PCA9685. Module hỗ trợ điều khiển tối đa 2 động cơ DC và 8 servo motor với PWM 16-bit, cho phép điều khiển tốc độ và vị trí chính xác.

Động cơ là thành phần quan trọng nhất trong robotics, cho phép robot di chuyển và tương tác với môi trường. BeE Board tích hợp driver motor mạnh mẽ có thể điều khiển nhiều loại động cơ khác nhau.

**Ứng dụng thực tế:**

-   Robot di chuyển và tránh vật cản
-   <PERSON><PERSON><PERSON> tay robot và gripper
-   Xe điều khiển từ xa
-   <PERSON><PERSON> thống tự động hóa
-   <PERSON><PERSON><PERSON> in 3D và CNC mini
-   Camera gimbal và pan-tilt

## Thông số kỹ thuật

| Thông số         | Giá trị             |
| ---------------- | ------------------- |
| Chip điều khiển  | PCA9685             |
| Giao tiếp        | I2C (SCL, SDA)      |
| Địa chỉ I2C      | 0x40                |
| Số kênh PWM      | 16 (2 DC + 8 Servo) |
| Độ phân giải PWM | 16-bit (0-4095)     |
| Tần số PWM DC    | 3000Hz              |
| Tần số PWM Servo | 50Hz                |
| Điện áp động cơ  | 6V-12V              |
| Dòng tối đa      | 2A/kênh             |
| Góc servo        | 0-270°              |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from Motor import DCMotors, Servos

# Khởi tạo điều khiển DC motor
motors = DCMotors(port=bee.PORT1)

# Khởi tạo điều khiển servo
servos = Servos(port=bee.PORT1)
```

### Điều khiển DC Motor

#### `speed(motor_index, power)` - Đặt tốc độ

```python
# Động cơ 0 tiến với tốc độ 30%
motors.speed(0, 30)

# Động cơ 1 lùi với tốc độ 25%
motors.speed(1, -25)

# Dừng động cơ 0
motors.speed(0, 0)
```

#### `brake(motor_index)` - Phanh động cơ

```python
# Phanh động cơ 0
motors.brake(0)

# Phanh tất cả động cơ
motors.brake(0)
motors.brake(1)
```

### Điều khiển Servo Motor

#### `position(index, degrees)` - Đặt vị trí góc

```python
# Servo 0 quay đến 90 độ
servos.position(0, 90)

# Servo 1 quay đến 180 độ
servos.position(1, 180)

# Quay về vị trí 0 độ
servos.position(0, 0)
```

#### `release(index)` - Thả servo

```python
# Thả servo 0 (không giữ vị trí)
servos.release(0)
```

## Ví dụ Blockly

```blockly
when program starts:
    set motor 0 speed to 50%
    set motor 1 speed to 50%
    wait 2 seconds

    set servo 0 to 90 degrees
    set servo 1 to 45 degrees
    wait 1 second

    brake all motors

when button A pressed:
    repeat 4 times:
        set motor 0 speed to 30%
        set motor 1 speed to -30%
        wait 1 second
        brake all motors
        wait 0.5 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Robot di chuyển cơ bản

```python
from BeeBrain import bee
from Motor import DCMotors
from Button import Button
import time

# Khởi tạo
motors = DCMotors(port=bee.PORT1)
button = Button(36)

class SimpleRobot:
    def __init__(self):
        self.is_moving = False

    def move_forward(self, speed=30):
        """Di chuyển tiến"""
        motors.speed(0, speed)
        motors.speed(1, speed)
        print(f"Moving forward at {speed}%")

    def move_backward(self, speed=25):
        """Di chuyển lùi"""
        motors.speed(0, -speed)
        motors.speed(1, -speed)
        print(f"Moving backward at {speed}%")

    def turn_left(self, speed=25):
        """Rẽ trái"""
        motors.speed(0, -speed)
        motors.speed(1, speed)
        print("Turning left")

    def turn_right(self, speed=25):
        """Rẽ phải"""
        motors.speed(0, speed)
        motors.speed(1, -speed)
        print("Turning right")

    def stop(self):
        """Dừng robot"""
        motors.brake(0)
        motors.brake(1)
        print("Robot stopped")

    def demo_movements(self):
        """Demo các chuyển động cơ bản"""
        movements = [
            ("Forward", self.move_forward, 2),
            ("Backward", self.move_backward, 2),
            ("Turn Left", self.turn_left, 1),
            ("Turn Right", self.turn_right, 1),
            ("Stop", self.stop, 1)
        ]

        for name, action, duration in movements:
            print(f"=== {name} ===")
            action()
            time.sleep(duration)

        self.stop()

def toggle_movement(pin):
    """Toggle robot movement"""
    global robot
    if robot.is_moving:
        robot.stop()
        robot.is_moving = False
    else:
        robot.move_forward()
        robot.is_moving = True

# Tạo robot và đăng ký sự kiện
robot = SimpleRobot()
button.on_press(toggle_movement)

# Chạy demo
robot.demo_movements()

# Chờ tương tác
print("Press Button A to toggle movement")
while True:
    time.sleep(0.1)
```

### Ví dụ nâng cao - Robot với cánh tay servo

```python
from BeeBrain import bee
from Motor import DCMotors, Servos
from Ultrasonic import HCSR04
from Button import Button
import time
import math

# Khởi tạo các module
motors = DCMotors(bee.PORT1)
servos = Servos(bee.PORT2)
sensor = HCSR04(bee.PORT3)
button = bee.buttonA

class RobotArm:
    def __init__(self):
        # Vị trí servo (base, shoulder, elbow, gripper)
        self.base_angle = 90      # 0-180°
        self.shoulder_angle = 90  # 0-180°
        self.elbow_angle = 90     # 0-180°
        self.gripper_angle = 90   # 0-180° (0=mở, 180=đóng)

        # Khởi tạo vị trí home
        self.home_position()

    def home_position(self):
        """Về vị trí home"""
        self.base_angle = 90
        self.shoulder_angle = 90
        self.elbow_angle = 90
        self.gripper_angle = 90
        self.update_servos()
        print("Robot arm at home position")

    def update_servos(self):
        """Cập nhật vị trí tất cả servo"""
        servos.position(0, self.base_angle)
        servos.position(1, self.shoulder_angle)
        servos.position(2, self.elbow_angle)
        servos.position(3, self.gripper_angle)
        time.sleep(0.5)  # Chờ servo di chuyển

    def rotate_base(self, angle):
        """Xoay base"""
        self.base_angle = max(0, min(180, angle))
        servos.position(0, self.base_angle)
        print(f"Base rotated to {self.base_angle}°")

    def move_shoulder(self, angle):
        """Di chuyển shoulder"""
        self.shoulder_angle = max(0, min(180, angle))
        servos.position(1, self.shoulder_angle)
        print(f"Shoulder moved to {self.shoulder_angle}°")

    def move_elbow(self, angle):
        """Di chuyển elbow"""
        self.elbow_angle = max(0, min(180, angle))
        servos.position(2, self.elbow_angle)
        print(f"Elbow moved to {self.elbow_angle}°")

    def control_gripper(self, angle):
        """Điều khiển gripper"""
        self.gripper_angle = max(0, min(180, angle))
        servos.position(3, self.gripper_angle)
        print(f"Gripper set to {self.gripper_angle}°")

    def grab_object(self):
        """Sequence gắp vật"""
        print("=== Grabbing object ===")

        # Mở gripper
        self.control_gripper(0)
        time.sleep(1)

        # Hạ cánh tay
        self.move_shoulder(45)
        self.move_elbow(135)
        time.sleep(2)

        # Đóng gripper
        self.control_gripper(180)
        time.sleep(1)

        # Nâng lên
        self.move_shoulder(90)
        self.move_elbow(90)
        time.sleep(2)

        print("Object grabbed!")

    def release_object(self):
        """Sequence thả vật"""
        print("=== Releasing object ===")

        # Hạ xuống
        self.move_shoulder(45)
        self.move_elbow(135)
        time.sleep(2)

        # Mở gripper
        self.control_gripper(0)
        time.sleep(1)

        # Về home
        self.home_position()
        time.sleep(1)

        print("Object released!")

    def wave_gesture(self):
        """Gesture vẫy tay"""
        print("=== Waving ===")

        # Nâng cánh tay
        self.move_shoulder(135)
        self.move_elbow(45)
        time.sleep(1)

        # Vẫy tay
        for _ in range(3):
            self.rotate_base(60)
            time.sleep(0.5)
            self.rotate_base(120)
            time.sleep(0.5)

        # Về home
        self.home_position()
        print("Wave completed!")

class MobileRobot:
    def __init__(self):
        self.arm = RobotArm()
        self.scanning = False

    def scan_environment(self):
        """Quét môi trường bằng ultrasonic"""
        print("=== Environment Scanning ===")

        distances = []
        angles = range(0, 181, 30)  # Quét từ 0° đến 180°, mỗi 30°

        for angle in angles:
            # Xoay sensor (giả sử gắn trên base)
            self.arm.rotate_base(angle)
            time.sleep(1)

            # Đo khoảng cách
            distance = sensor.distance_cm()
            distances.append((angle, distance))

            print(f"Angle {angle}°: {distance:.1f}cm")

        # Tìm vật thể gần nhất
        valid_distances = [(a, d) for a, d in distances if d != -1 and d < 50]

        if valid_distances:
            closest = min(valid_distances, key=lambda x: x[1])
            print(f"Closest object at {closest[0]}°, distance {closest[1]:.1f}cm")

            # Xoay về hướng vật thể gần nhất
            self.arm.rotate_base(closest[0])
            time.sleep(1)

            return closest
        else:
            print("No objects detected in range")
            return None

    def autonomous_grab(self):
        """Tự động tìm và gắp vật"""
        print("=== Autonomous Grab Mode ===")

        # Quét môi trường
        target = self.scan_environment()

        if target:
            angle, distance = target

            if distance < 30:  # Đủ gần để gắp
                print("Object in range, attempting grab...")
                self.arm.grab_object()

                # Di chuyển robot lùi một chút
                motors.speed(0, -20)
                motors.speed(1, -20)
                time.sleep(1)
                motors.brake(0)
                motors.brake(1)

                # Thả vật ở vị trí khác
                time.sleep(2)
                self.arm.release_object()

            else:
                print("Object too far, moving closer...")
                # Di chuyển gần hơn
                motors.speed(0, 25)
                motors.speed(1, 25)
                time.sleep(1)
                motors.brake(0)
                motors.brake(1)

        # Về home position
        self.arm.home_position()

    def demo_sequence(self):
        """Demo tất cả chức năng"""
        sequences = [
            ("Wave Gesture", self.arm.wave_gesture),
            ("Environment Scan", self.scan_environment),
            ("Autonomous Grab", self.autonomous_grab)
        ]

        for name, func in sequences:
            print(f"\n{'='*20}")
            print(f"Starting: {name}")
            print('='*20)

            func()
            time.sleep(2)

        print("\nDemo completed!")

def button_handler(pin):
    """Xử lý sự kiện nút bấm"""
    global robot, demo_running

    if not demo_running:
        print("Starting demo sequence...")
        demo_running = True
        robot.demo_sequence()
        demo_running = False
    else:
        print("Demo already running...")

# Khởi tạo robot và biến trạng thái
robot = MobileRobot()
demo_running = False

# Đăng ký sự kiện nút bấm
button.on_press(button_handler)

print("Mobile Robot with Arm initialized!")
print("Press Button A to start demo sequence")
print("Press Ctrl+C to exit")

try:
    while True:
        time.sleep(0.1)
except KeyboardInterrupt:
    print("\nShutting down robot...")
    motors.brake(0)
    motors.brake(1)
    robot.arm.home_position()
    print("Robot stopped safely")
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Robot class**: Tổ chức code thành class để dễ quản lý
2. **Movement methods**: Các phương thức di chuyển cơ bản (tiến, lùi, rẽ)
3. **Demo sequence**: Trình diễn các chuyển động theo thứ tự
4. **Button control**: Điều khiển bằng nút bấm

### Ví dụ nâng cao:

1. **Multi-axis control**: Điều khiển nhiều servo cho cánh tay robot
2. **Coordinate system**: Quản lý vị trí các khớp cánh tay
3. **Sensor integration**: Kết hợp ultrasonic để quét môi trường
4. **Autonomous behavior**: Robot tự động tìm và gắp vật

## Bài tập mở rộng

1. **Robot vẽ tranh**: Lập trình robot vẽ các hình đơn giản bằng bút
2. **Xe tự lái**: Tạo xe tự động đi theo đường kẻ sẵn
3. **Robot phân loại**: Robot tự động phân loại vật theo màu sắc hoặc kích thước

## Lỗi thường gặp

```{admonition} Lỗi: Động cơ không quay
:class: warning

**Nguyên nhân**: Nguồn không đủ hoặc kết nối I2C sai

**Giải pháp**:
- Kiểm tra nguồn 6V-12V cho động cơ
- Đảm bảo kết nối I2C (SCL, SDA) đúng
- Kiểm tra địa chỉ I2C (0x40)
- Thử với tốc độ thấp trước
```

```{admonition} Lỗi: Servo rung lắc hoặc không giữ vị trí
:class: warning

**Nguyên nhân**: Tần số PWM sai hoặc nguồn không ổn định

**Giải pháp**:
- Đảm bảo tần số servo = 50Hz
- Sử dụng nguồn riêng cho servo (5V-6V)
- Thêm tụ lọc nguồn
- Kiểm tra giá trị pulse width (600-2400µs)
```

```{admonition} Lỗi: Robot di chuyển không thẳng
:class: warning

**Nguyên nhân**: Động cơ không đồng bộ hoặc bánh xe khác nhau

**Giải pháp**:
- Hiệu chỉnh tốc độ từng động cơ riêng
- Kiểm tra áp suất bánh xe
- Cân bằng trọng lượng robot
- Sử dụng encoder để feedback
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [PCA9685 Datasheet](https://cdn-shop.adafruit.com/datasheets/PCA9685.pdf)
-   [DC Motor Control Theory](https://learn.adafruit.com/adafruit-motor-hat-for-raspberry-pi)
-   [Servo Motor Guide](https://learn.adafruit.com/adafruit-arduino-lesson-14-servo-motors)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
