# MPU6050 - <PERSON><PERSON><PERSON>ến IMU 6 Trục

## Giới thiệu

MPU6050 là module cảm biến IMU (Inertial Measurement Unit) 6 trục sử dụng chip MPU6050. <PERSON><PERSON><PERSON> tích hợp cảm biến gia tốc 3 trục và con quay hồi chuyển (gyroscope) 3 tr<PERSON><PERSON>, cho phép đo chuyển động, góc nghiêng và rung lắc với độ chính xác cao.

Module sử dụng giao tiếp I2C và có thể tính toán gó<PERSON> (pitch, roll, yaw) thông qua bộ lọc bổ sung (complementary filter). Đặc biệt hữu ích cho các dự án robotics, drone, hệ thống cân bằng và game điều khiển chuyển động.

**Ứng dụng thực tế:**

-   Robot cân bằng 2 bánh (self-balancing robot)
-   <PERSON>one và quadcopter điều khiển
-   <PERSON><PERSON> thống chống rung cho camera
-   Game điều khiển bằng chuyển động
-   <PERSON>hi<PERSON><PERSON> bị theo dõi hoạt động thể thao
-   <PERSON><PERSON> thống cảnh báo ngã cho người già
-   Máy đo độ nghiêng chính xác

## Thông số kỹ thuật

| Thông số            | Giá trị                              |
| ------------------- | ------------------------------------ |
| Chip cảm biến       | MPU6050                              |
| Giao tiếp           | I2C                                  |
| Địa chỉ I2C         | 0x68 (mặc định)                      |
| Điện áp hoạt động   | 3.3V - 5V                            |
| Dòng tiêu thụ       | 3.9mA (hoạt động), 5μA (sleep)       |
| Gia tốc kế          | ±2g, ±4g, ±8g, ±16g                  |
| Con quay hồi chuyển | ±250°/s, ±500°/s, ±1000°/s, ±2000°/s |
| Độ phân giải ADC    | 16-bit                               |
| Tần số lấy mẫu      | 4Hz - 8kHz                           |
| Cảm biến nhiệt độ   | -40°C đến +85°C                      |
| Độ ổn định          | ±1% trong 0°C đến 40°C               |
| Kích thước          | 21mm x 16mm                          |

## Giao diện lập trình

### Khởi tạo

```python
from Mpu6050 import MPU6050
from BeeBrain import bee

# Khởi tạo với I2C mặc định
imu = bee.imu

# Khởi tạo với BeeBrain
imu = MPU6050(port=bee.PORT1)
```

### Đọc dữ liệu cảm biến

#### `accel` - Đọc gia tốc 3 trục

```python
# Đọc gia tốc (m/s²)
acceleration = imu.accel
print(f"Accel X: {acceleration.x}")
print(f"Accel Y: {acceleration.y}")
print(f"Accel Z: {acceleration.z}")
```

#### `gyro` - Đọc con quay hồi chuyển

```python
# Đọc tốc độ góc (°/s)
gyroscope = imu.gyro
print(f"Gyro X: {gyroscope.x}")
print(f"Gyro Y: {gyroscope.y}")
print(f"Gyro Z: {gyroscope.z}")
```

#### `temperature` - Đọc nhiệt độ

```python
# Đọc nhiệt độ chip (°C)
temp = imu.temperature
print(f"Temperature: {temp}°C")
```

### Tính toán góc Euler

#### `pitchDeg`, `rollDeg`, `yawDeg` - Góc nghiêng (độ)

```python
# Cập nhật dữ liệu trước khi đọc góc
imu.update()

# Đọc góc nghiêng (độ)
pitch = imu.pitchDeg  # Góc pitch (lên/xuống)
roll = imu.rollDeg    # Góc roll (trái/phải)
yaw = imu.yawDeg      # Góc yaw (xoay)

print(f"Pitch: {pitch}°")
print(f"Roll: {roll}°")
print(f"Yaw: {yaw}°")
```

### Chức năng bổ sung

#### `update()` - Cập nhật dữ liệu

```python
# Cập nhật tất cả dữ liệu cảm biến
imu.update()
```

#### `calibrate_gyro()` - Hiệu chuẩn con quay

```python
# Hiệu chuẩn gyroscope (giữ cảm biến đứng yên)
print("Calibrating gyro... Keep sensor still!")
imu.calibrate_gyro()
print("Calibration complete!")
```

#### `is_shaking()` - Phát hiện rung lắc

```python
# Kiểm tra có rung lắc không
if imu.is_shaking():
    print("Sensor is shaking!")
else:
    print("Sensor is stable")
```

## Ví dụ Blockly

```blockly
when program starts:
    set imu to MPU6050
    calibrate imu gyro

forever:
    update imu data

    set pitch to imu pitch degrees
    set roll to imu roll degrees

    if pitch > 30:
        display "Tilted Forward"
        set LED to RED
    else if pitch < -30:
        display "Tilted Backward"
        set LED to BLUE
    else if roll > 30:
        display "Tilted Right"
        set LED to GREEN
    else if roll < -30:
        display "Tilted Left"
        set LED to YELLOW
    else:
        display "Level"
        set LED to WHITE

    wait 0.1 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Đo góc nghiêng

```python
from Mpu6050 import MPU6050
from BeeBrain import bee
import time
import math

def setup():
    """Khởi tạo cảm biến IMU"""
    global imu

    try:
        # Khởi tạo IMU
        imu = bee.imu

        # Hiệu chuẩn gyroscope
        bee.oled.clear()
        bee.oled.write("Calibrating...", 15, 10)
        bee.oled.write("Keep still!", 20, 25)

        imu.calibrate_gyro()

        # Test đọc dữ liệu
        imu.update()
        accel = imu.accel
        temp = imu.temperature

        bee.oled.clear()
        bee.oled.write("IMU Ready!", 20, 10)
        bee.oled.write(f"Temp: {temp:.1f}C", 15, 25)
        bee.oled.write(f"Accel: {accel.x:.1f}", 10, 40)

        time.sleep(2)
        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("IMU Error!", 20, 10)
        bee.oled.write(str(e)[:20], 5, 25)
        return False

def tilt_monitor():
    """Theo dõi góc nghiêng"""
    while True:
        try:
            # Cập nhật dữ liệu IMU
            imu.update()

            # Đọc góc nghiêng
            pitch = imu.pitchDeg
            roll = imu.rollDeg
            yaw = imu.yawDeg

            # Tính tổng độ nghiêng
            total_tilt = math.sqrt(pitch*pitch + roll*roll)

            # Hiển thị thông tin
            bee.oled.clear()
            bee.oled.write("Tilt Monitor", 15, 0)
            bee.oled.write(f"Pitch: {pitch:.1f}°", 5, 15)
            bee.oled.write(f"Roll:  {roll:.1f}°", 5, 30)
            bee.oled.write(f"Total: {total_tilt:.1f}°", 5, 45)

            # LED phản hồi theo góc nghiêng
            if total_tilt < 10:
                # Gần như thẳng - LED xanh lá
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            elif total_tilt < 30:
                # Nghiêng nhẹ - LED vàng
                bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)
            elif total_tilt < 60:
                # Nghiêng nhiều - LED cam
                bee.neopixel.set_rgb(bee.LED1, 255, 128, 0)
            else:
                # Nghiêng quá nhiều - LED đỏ
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                bee.buzzer.play_tone("C4", 0.1)  # Cảnh báo

            # Kiểm tra rung lắc
            if imu.is_shaking():
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)  # Tím
                bee.buzzer.play_tone("A4", 0.05)

            # Kiểm tra nút thoát
            if bee.is_button_pressed(bee.BUTTON_B):
                break

            time.sleep(0.1)

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Read Error!", 15, 10)
            time.sleep(1)

def motion_detection():
    """Phát hiện chuyển động"""
    motion_threshold = 2.0  # m/s² ngưỡng phát hiện chuyển động
    motion_count = 0

    while True:
        try:
            imu.update()
            accel = imu.accel
            gyro = imu.gyro

            # Tính tổng gia tốc (loại bỏ trọng lực)
            total_accel = math.sqrt(accel.x*accel.x + accel.y*accel.y + (accel.z-9.8)*(accel.z-9.8))

            # Tính tổng tốc độ góc
            total_gyro = math.sqrt(gyro.x*gyro.x + gyro.y*gyro.y + gyro.z*gyro.z)

            # Phát hiện chuyển động
            is_moving = total_accel > motion_threshold or total_gyro > 50

            if is_moving:
                motion_count += 1
            else:
                motion_count = max(0, motion_count - 1)

            # Hiển thị trạng thái
            bee.oled.clear()
            bee.oled.write("Motion Detect", 15, 0)
            bee.oled.write(f"Accel: {total_accel:.1f}", 5, 15)
            bee.oled.write(f"Gyro:  {total_gyro:.1f}", 5, 30)
            bee.oled.write(f"Motion: {motion_count}", 5, 45)

            # LED và âm thanh phản hồi
            if motion_count > 5:
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # Đỏ - chuyển động
                if motion_count == 6:  # Chỉ beep một lần
                    bee.buzzer.play_tone("E4", 0.1)
            elif motion_count > 0:
                bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng - có thể chuyển động
            else:
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # Xanh - đứng yên

            if bee.is_button_pressed(bee.BUTTON_B):
                break

            time.sleep(0.1)

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Motion Error!", 15, 10)
            time.sleep(1)

# Chạy chương trình
if setup():
    print("1. Tilt Monitor")
    print("2. Motion Detection")

    # Demo tự động
    tilt_monitor()
    time.sleep(1)
    motion_detection()
```

### Ví dụ nâng cao - Robot cân bằng

```python
from Mpu6050 import MPU6050
from BeeBrain import bee
import time
import math

class BalancingRobot:
    def __init__(self):
        # Khởi tạo IMU
        self.imu = bee.imu

        # Thông số PID controller
        self.kp = 30.0    # Proportional gain
        self.ki = 0.5     # Integral gain
        self.kd = 2.0     # Derivative gain

        # Biến PID
        self.previous_error = 0
        self.integral = 0
        self.target_angle = 0  # Góc cân bằng mục tiêu

        # Giới hạn motor
        self.max_motor_speed = 80
        self.min_motor_speed = -80

        # Trạng thái robot
        self.is_balancing = False
        self.fall_angle_limit = 45  # Góc ngã (độ)
        self.balance_start_angle = 15  # Góc bắt đầu cân bằng

        # Thống kê
        self.balance_time = 0
        self.max_balance_time = 0
        self.fall_count = 0

    def setup(self):
        """Khởi tạo hệ thống cân bằng"""
        try:
            # Hiệu chuẩn IMU
            bee.oled.clear()
            bee.oled.write("Calibrating IMU", 10, 10)
            bee.oled.write("Keep robot", 20, 25)
            bee.oled.write("upright & still", 15, 40)

            self.imu.calibrate_gyro()
            time.sleep(2)

            # Test motor
            bee.oled.clear()
            bee.oled.write("Testing motors", 15, 20)

            bee.motor.speed(0, 30)   # Motor trái
            bee.motor.speed(1, 30)   # Motor phải
            time.sleep(0.5)

            bee.motor.speed(0, -30)
            bee.motor.speed(1, -30)
            time.sleep(0.5)

            bee.motor.speed(0, 0)
            bee.motor.speed(1, 0)

            # Sẵn sàng
            bee.oled.clear()
            bee.oled.write("Balance Robot", 15, 0)
            bee.oled.write("Ready!", 25, 20)
            bee.oled.write("A: Start/Stop", 10, 35)
            bee.oled.write("B: Reset", 20, 50)

            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4")

            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Setup Error!", 15, 10)
            bee.oled.write(str(e)[:20], 5, 25)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def read_tilt_angle(self):
        """Đọc góc nghiêng cho cân bằng"""
        self.imu.update()

        # Sử dụng pitch angle (góc nghiêng trước/sau)
        angle = self.imu.pitchDeg

        # Lọc nhiễu đơn giản
        if abs(angle) > 90:
            angle = 90 if angle > 0 else -90

        return angle

    def calculate_pid(self, current_angle):
        """Tính toán PID control"""
        # Tính error
        error = self.target_angle - current_angle

        # Proportional term
        p_term = self.kp * error

        # Integral term (với giới hạn để tránh windup)
        self.integral += error
        self.integral = max(-100, min(100, self.integral))  # Giới hạn integral
        i_term = self.ki * self.integral

        # Derivative term
        derivative = error - self.previous_error
        d_term = self.kd * derivative

        # Tổng PID output
        pid_output = p_term + i_term + d_term

        # Cập nhật previous error
        self.previous_error = error

        return pid_output, error

    def control_motors(self, pid_output):
        """Điều khiển motor dựa trên PID output"""
        # Giới hạn tốc độ motor
        motor_speed = max(self.min_motor_speed,
                         min(self.max_motor_speed, pid_output))

        # Điều khiển motor (cùng chiều để cân bằng)
        bee.motor.speed(0, int(motor_speed))   # Motor trái
        bee.motor.speed(1, int(motor_speed))   # Motor phải

        return motor_speed

    def check_fall_condition(self, angle):
        """Kiểm tra điều kiện ngã"""
        return abs(angle) > self.fall_angle_limit

    def check_balance_start(self, angle):
        """Kiểm tra điều kiện bắt đầu cân bằng"""
        return abs(angle) < self.balance_start_angle

    def display_status(self, angle, motor_speed, error):
        """Hiển thị trạng thái robot"""
        bee.oled.clear()

        if self.is_balancing:
            bee.oled.write("BALANCING", 20, 0)
            bee.oled.write(f"Angle: {angle:.1f}°", 5, 15)
            bee.oled.write(f"Motor: {motor_speed:.0f}", 5, 30)
            bee.oled.write(f"Time: {self.balance_time:.1f}s", 5, 45)
        else:
            bee.oled.write("STOPPED", 25, 0)
            bee.oled.write(f"Angle: {angle:.1f}°", 5, 15)
            bee.oled.write(f"Max: {self.max_balance_time:.1f}s", 5, 30)
            bee.oled.write(f"Falls: {self.fall_count}", 5, 45)

    def emergency_stop(self):
        """Dừng khẩn cấp"""
        self.is_balancing = False
        bee.motor.speed(0, 0)
        bee.motor.speed(1, 0)

        # Reset PID
        self.integral = 0
        self.previous_error = 0

        # LED cảnh báo
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
        bee.buzzer.play_tone("C3", 0.5)

    def balance_cycle(self):
        """Chu trình cân bằng chính"""
        start_time = time.time()

        while self.is_balancing:
            try:
                # Đọc góc nghiêng
                current_angle = self.read_tilt_angle()

                # Kiểm tra điều kiện ngã
                if self.check_fall_condition(current_angle):
                    self.fall_count += 1
                    self.emergency_stop()

                    bee.oled.clear()
                    bee.oled.write("ROBOT FELL!", 15, 15)
                    bee.oled.write(f"Angle: {current_angle:.1f}°", 5, 30)
                    time.sleep(2)
                    break

                # Tính PID
                pid_output, error = self.calculate_pid(current_angle)

                # Điều khiển motor
                motor_speed = self.control_motors(pid_output)

                # Cập nhật thời gian cân bằng
                self.balance_time = time.time() - start_time
                if self.balance_time > self.max_balance_time:
                    self.max_balance_time = self.balance_time

                # Hiển thị trạng thái
                self.display_status(current_angle, motor_speed, error)

                # LED phản hồi
                if abs(error) < 2:
                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # Xanh - cân bằng tốt
                elif abs(error) < 5:
                    bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng - cân bằng khá
                else:
                    bee.neopixel.set_rgb(bee.LED1, 255, 128, 0)  # Cam - cân bằng kém

                # Kiểm tra nút dừng
                if bee.is_button_pressed(bee.BUTTON_A):
                    self.is_balancing = False
                    break

                time.sleep(0.01)  # 100Hz control loop

            except Exception as e:
                self.emergency_stop()
                bee.oled.clear()
                bee.oled.write("Balance Error!", 15, 10)
                bee.oled.write(str(e)[:20], 5, 25)
                time.sleep(2)
                break

        # Dừng motor khi kết thúc
        bee.motor.speed(0, 0)
        bee.motor.speed(1, 0)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        while True:
            try:
                # Đọc góc hiện tại
                current_angle = self.read_tilt_angle()

                # Hiển thị trạng thái
                self.display_status(current_angle, 0, current_angle)

                # Kiểm tra nút bấm
                if bee.is_button_pressed(bee.BUTTON_A):
                    if not self.is_balancing:
                        # Kiểm tra điều kiện bắt đầu
                        if self.check_balance_start(current_angle):
                            self.is_balancing = True
                            self.balance_time = 0

                            bee.oled.clear()
                            bee.oled.write("Starting...", 20, 20)
                            time.sleep(1)

                            # Bắt đầu cân bằng
                            self.balance_cycle()
                        else:
                            bee.oled.clear()
                            bee.oled.write("Angle too large!", 10, 15)
                            bee.oled.write("Stand robot up", 15, 30)
                            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                            time.sleep(2)

                    time.sleep(0.3)  # Debounce

                elif bee.is_button_pressed(bee.BUTTON_B):
                    # Reset thống kê
                    self.balance_time = 0
                    self.max_balance_time = 0
                    self.fall_count = 0
                    self.integral = 0
                    self.previous_error = 0

                    bee.oled.clear()
                    bee.oled.write("Stats Reset!", 20, 20)
                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 255)
                    time.sleep(1)

                time.sleep(0.1)

            except KeyboardInterrupt:
                break

        # Cleanup
        self.emergency_stop()

# Chạy robot cân bằng
if __name__ == "__main__":
    robot = BalancingRobot()
    robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **IMU calibration**: Hiệu chuẩn gyroscope để loại bỏ drift
2. **Angle calculation**: Tính góc nghiêng từ dữ liệu accelerometer
3. **Motion detection**: Phát hiện chuyển động dựa trên ngưỡng gia tốc
4. **Visual feedback**: LED và OLED hiển thị trạng thái

### Ví dụ nâng cao:

1. **PID controller**: Thuật toán điều khiển PID cho cân bằng
2. **Fall detection**: Phát hiện và xử lý tình huống robot ngã
3. **Motor control**: Điều khiển motor DC để duy trì cân bằng
4. **Statistics tracking**: Theo dõi thời gian cân bằng và số lần ngã
5. **Safety features**: Dừng khẩn cấp và giới hạn an toàn

## Bài tập mở rộng

1. **Gesture control system**: Hệ thống điều khiển bằng cử chỉ tay
2. **Earthquake detector**: Máy phát hiện động đất mini
3. **Activity tracker**: Thiết bị theo dõi hoạt động thể thao

## Lỗi thường gặp

```{admonition} Lỗi: Góc nghiêng không chính xác
:class: warning

**Nguyên nhân**: Chưa hiệu chuẩn gyroscope hoặc nhiễu từ trường

**Giải pháp**:
- Gọi calibrate_gyro() khi khởi tạo
- Giữ cảm biến đứng yên trong quá trình hiệu chuẩn
- Tránh xa nguồn từ trường mạnh (loa, motor lớn)
- Sử dụng complementary filter để lọc nhiễu
```

```{admonition} Lỗi: Dữ liệu IMU không ổn định
:class: warning

**Nguyên nhân**: Rung động cơ học hoặc nhiễu điện

**Giải pháp**:
- Gắn cảm biến chắc chắn, tránh rung lắc
- Sử dụng capacitor lọc nguồn (100μF + 0.1μF)
- Đặt cảm biến xa motor và nguồn switching
- Thêm bộ lọc thông thấp trong software
```

```{admonition} Lỗi: I2C communication timeout
:class: warning

**Nguyên nhân**: Kết nối I2C không ổn định

**Giải pháp**:
- Kiểm tra kết nối SDA, SCL và GND
- Thêm pull-up resistor 4.7kΩ cho SDA/SCL
- Giảm tốc độ I2C xuống 100kHz
- Kiểm tra không có xung đột địa chỉ I2C
```

## Tài nguyên tham khảo

-   [MPU6050 Datasheet](https://invensense.tdk.com/wp-content/uploads/2015/02/MPU-6000-Datasheet1.pdf)
-   [Complementary Filter Guide](https://www.pieter-jan.com/node/11)
-   [PID Control Tutorial](https://en.wikipedia.org/wiki/PID_controller)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
