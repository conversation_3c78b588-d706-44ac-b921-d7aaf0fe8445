# DigitalIO - Mo<PERSON>le <PERSON>ển GPIO Đơn Giản

## Giới thiệu

DigitalIO là module wrapper đơn giản cho việc điều khiển GPIO (General Purpose Input/Output) trên BeE Board. Module này cung cấp giao diện dễ sử dụng để điều khiển các chân GPIO với các hàm cơ bản như bật/tắt, thiết lập mức cao/thấp.

Module được thiết kế để đơn giản hóa việc điều khiển LED, relay, buzzer và các thiết bị digital khác. Đ<PERSON><PERSON> là module cơ bản nhất trong hệ sinh thái BeE Board, phù hợp cho người mới bắt đầu học lập trình embedded.

**Ứng dụng thực tế:**

-   <PERSON><PERSON><PERSON><PERSON>hiể<PERSON> LED đơn giản
-   Bật/tắt relay, solenoid
-   Đ<PERSON>ều khiển buzzer on/off
-   Tín hiệu điều khiển digital
-   Giao tiếp với module khác
-   Dự án IoT cơ bản
-   Học tập lập trình embedded

## Thông số kỹ thuật

| Thông số          | Giá trị              |
| ----------------- | -------------------- |
| Loại chân         | Digital Output/Input |
| Điện áp logic     | 3.3V                 |
| Dòng tối đa       | 12mA/pin             |
| Tốc độ chuyển đổi | >1MHz                |
| Mức logic cao     | HIGH = 1 (3.3V)      |
| Mức logic thấp    | LOW = 0 (0V)         |
| Pull-up/Pull-down | Có hỗ trợ (hardware) |
| Chế độ hoạt động  | Output/Input         |
| Tương thích       | 5V tolerant (input)  |

## Giao diện lập trình

### Khởi tạo

```python
from DigitalIO import DigitalIO

# Sử dụng với BeeBrain
from BeeBrain import bee
gpio_pin = DigitalIO(port=bee.PORT1)
```

### Điều khiển cơ bản

#### `high()` / `on()` - Thiết lập mức cao

```python
# Thiết lập pin ở mức cao (3.3V)
gpio_pin.high()

# Tương đương với high()
gpio_pin.on()
```

#### `low()` / `off()` - Thiết lập mức thấp

```python
# Thiết lập pin ở mức thấp (0V)
gpio_pin.low()

# Tương đương với low()
gpio_pin.off()
```

#### `set(value)` - Thiết lập giá trị

```python
# Sử dụng hằng số
gpio_pin.set(DigitalIO.HIGH)  # Mức cao
gpio_pin.set(DigitalIO.LOW)   # Mức thấp

# Sử dụng số
gpio_pin.set(1)  # Mức cao
gpio_pin.set(0)  # Mức thấp

# Sử dụng boolean
gpio_pin.set(True)   # Mức cao
gpio_pin.set(False)  # Mức thấp
```

### Đọc trạng thái

#### `value` - Đọc trạng thái hiện tại

```python
# Đọc trạng thái pin
current_state = gpio_pin.value
print(f"Pin state: {current_state}")

# Sử dụng trong điều kiện
if gpio_pin.value == DigitalIO.HIGH:
    print("Pin is HIGH")
```

## Ví dụ Blockly

```blockly
when program starts:
    set led_module at PORT1

forever:
    turn led_module ON
    wait 1 second
    turn led_module OFF
    wait 1 second

when button A pressed:
    if led_module is HIGH:
        turn led_module OFF
    else:
        turn led_module ON
```

## Ví dụ Python

### Ví dụ cơ bản - LED nhấp nháy

```python
from DigitalIO import DigitalIO
from BeeBrain import bee
import time

def setup():
    """Khởi tạo GPIO cho LED"""
    global led

    try:
        # Khởi tạo GPIO cho LED
        led = DigitalIO(port=bee.PORT1)  # PORT 1

        # Test LED
        led.on()
        time.sleep(0.5)
        led.off()

        bee.oled.clear()
        bee.oled.write("GPIO LED", 20, 10)
        bee.oled.write("Ready!", 25, 25)

        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("GPIO Error!", 15, 10)
        return False

def blink_led():
    """LED nhấp nháy cơ bản"""
    blink_count = 0

    while True:
        # Bật LED
        led.high()
        bee.oled.clear()
        bee.oled.write("LED ON", 25, 10)
        bee.oled.write(f"Count: {blink_count}", 15, 25)

        time.sleep(1)

        # Tắt LED
        led.low()
        bee.oled.clear()
        bee.oled.write("LED OFF", 20, 10)
        bee.oled.write(f"Count: {blink_count}", 15, 25)

        time.sleep(1)
        blink_count += 1

        # Kiểm tra nút dừng
        if bee.buttonB.is_pressed():
            led.off()
            break

def interactive_control():
    """Điều khiển LED bằng nút bấm"""
    while True:
        # Hiển thị trạng thái
        led_state = "ON" if led.value == DigitalIO.HIGH else "OFF"

        bee.oled.clear()
        bee.oled.write("LED Control", 15, 0)
        bee.oled.write(f"State: {led_state}", 15, 15)
        bee.oled.write("A: Toggle", 15, 30)
        bee.oled.write("B: Exit", 20, 45)

        # Kiểm tra nút bấm
        if bee.buttonA.is_pressed():
            # Toggle LED
            if led.value == DigitalIO.HIGH:
                led.off()
            else:
                led.on()

            time.sleep(0.3)  # Debounce

        elif bee.buttonB.is_pressed():
            led.off()
            break

        time.sleep(0.1)

# Chạy chương trình
if setup():
    print("1. Blink LED")
    print("2. Interactive Control")

    # Demo tự động
    blink_led()
    time.sleep(1)
    interactive_control()
```

### Ví dụ nâng cao - Hệ thống điều khiển đa GPIO

```python
from DigitalIO import DigitalIO
from BeeBrain import bee
import time

class MultiGPIOController:
    def __init__(self):
        # Khởi tạo nhiều GPIO
        self.leds = [
            DigitalIO(port=bee.PORT1),   # LED 1
            DigitalIO(port=bee.PORT2),   # LED 2
            DigitalIO(port=bee.PORT3),   # LED 3
            DigitalIO(port=bee.PORT4),   # LED 4
        ]

        self.relay = DigitalIO(port=bee.PORT5)      # Relay
        self.buzzer = bee.buzzer
        self.status_led = bee.led1

        # Trạng thái hệ thống
        self.current_pattern = 0
        self.patterns = [
            "sequential", "alternate", "random", "wave", "all_on", "all_off"
        ]
        self.pattern_names = [
            "Sequential", "Alternate", "Random", "Wave", "All On", "All Off"
        ]

    def setup(self):
        """Khởi tạo hệ thống"""
        try:
            # Test tất cả GPIO
            self.all_on()
            time.sleep(0.5)
            self.all_off()

            # Status LED sáng
            self.status_led.on()

            bee.oled.clear()
            bee.oled.write("Multi GPIO", 15, 0)
            bee.oled.write("Controller", 15, 15)
            bee.oled.write("Ready!", 25, 30)

            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4")

            time.sleep(2)
            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Setup Error!", 15, 10)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def all_on(self):
        """Bật tất cả LED"""
        for led in self.leds:
            led.on()

    def all_off(self):
        """Tắt tất cả LED"""
        for led in self.leds:
            led.off()

    def pattern_sequential(self):
        """Pattern tuần tự"""
        for i, led in enumerate(self.leds):
            self.all_off()
            led.on()

            bee.oled.clear()
            bee.oled.write("Sequential", 15, 0)
            bee.oled.write(f"LED {i+1} ON", 20, 20)

            time.sleep(0.5)

    def pattern_alternate(self):
        """Pattern xen kẽ"""
        # Bật LED lẻ
        for i in range(0, len(self.leds), 2):
            self.leds[i].on()
        for i in range(1, len(self.leds), 2):
            self.leds[i].off()

        bee.oled.clear()
        bee.oled.write("Alternate", 20, 0)
        bee.oled.write("Odd LEDs", 20, 20)

        time.sleep(0.8)

        # Bật LED chẵn
        for i in range(0, len(self.leds), 2):
            self.leds[i].off()
        for i in range(1, len(self.leds), 2):
            self.leds[i].on()

        bee.oled.clear()
        bee.oled.write("Alternate", 20, 0)
        bee.oled.write("Even LEDs", 15, 20)

        time.sleep(0.8)

    def pattern_random(self):
        """Pattern ngẫu nhiên"""
        import random

        # Random bật/tắt từng LED
        for led in self.leds:
            if random.random() > 0.5:
                led.on()
            else:
                led.off()

        # Random relay và buzzer
        if random.random() > 0.7:
            self.relay.on()
            self.buzzer.on()
            time.sleep(0.1)
            self.buzzer.off()
        else:
            self.relay.off()

        bee.oled.clear()
        bee.oled.write("Random", 25, 0)
        bee.oled.write("Pattern", 25, 20)

        time.sleep(0.6)

    def pattern_wave(self):
        """Pattern sóng"""
        # Sóng từ trái sang phải
        for i in range(len(self.leds)):
            self.all_off()
            if i > 0:
                self.leds[i-1].on()
            self.leds[i].on()
            if i < len(self.leds) - 1:
                self.leds[i+1].on()

            bee.oled.clear()
            bee.oled.write("Wave", 30, 0)
            bee.oled.write(f"Position {i+1}", 15, 20)

            time.sleep(0.3)

        # Sóng từ phải sang trái
        for i in range(len(self.leds)-1, -1, -1):
            self.all_off()
            if i < len(self.leds) - 1:
                self.leds[i+1].on()
            self.leds[i].on()
            if i > 0:
                self.leds[i-1].on()

            time.sleep(0.3)

    def pattern_all_on(self):
        """Tất cả LED sáng"""
        self.all_on()
        self.relay.on()

        bee.oled.clear()
        bee.oled.write("All ON", 25, 0)
        bee.oled.write("Full Power", 15, 20)

        time.sleep(1)

    def pattern_all_off(self):
        """Tất cả LED tắt"""
        self.all_off()
        self.relay.off()

        bee.oled.clear()
        bee.oled.write("All OFF", 20, 0)
        bee.oled.write("Power Save", 15, 20)

        time.sleep(1)

    def run_pattern(self):
        """Chạy pattern hiện tại"""
        pattern_name = self.patterns[self.current_pattern]
        pattern_function = getattr(self, f"pattern_{pattern_name}")
        pattern_function()

    def next_pattern(self):
        """Chuyển sang pattern tiếp theo"""
        self.current_pattern = (self.current_pattern + 1) % len(self.patterns)

        # Hiển thị tên pattern
        pattern_name = self.pattern_names[self.current_pattern]
        bee.oled.clear()
        bee.oled.write("Pattern:", 20, 0)
        bee.oled.write(pattern_name, 10, 20)

        # Nhấp nháy status LED
        self.status_led.off()
        time.sleep(0.1)
        self.status_led.on()

        time.sleep(1)

    def emergency_stop(self):
        """Dừng khẩn cấp - tắt tất cả"""
        self.all_off()
        self.relay.off()
        self.buzzer.off()
        self.status_led.off()

        bee.oled.clear()
        bee.oled.write("EMERGENCY", 15, 0)
        bee.oled.write("STOP", 30, 20)

        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)

        # Buzzer cảnh báo
        for _ in range(3):
            self.buzzer.on()
            time.sleep(0.2)
            self.buzzer.off()
            time.sleep(0.2)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        while True:
            try:
                # Chạy pattern hiện tại
                self.run_pattern()

                # Kiểm tra nút bấm
                if bee.buttonA.is_pressed():
                    self.next_pattern()
                    time.sleep(0.3)  # Debounce

                elif bee.buttonB.is_pressed():
                    self.emergency_stop()
                    break

                time.sleep(0.1)

            except KeyboardInterrupt:
                self.emergency_stop()
                break

        # Cleanup
        self.all_off()
        self.relay.off()
        self.buzzer.off()
        self.status_led.off()

# Chạy hệ thống đa GPIO
if __name__ == "__main__":
    controller = MultiGPIOController()
    controller.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Simple LED control**: Điều khiển LED cơ bản với blink và toggle
2. **State reading**: Đọc và hiển thị trạng thái GPIO
3. **User interaction**: Tương tác với nút bấm
4. **Error handling**: Xử lý lỗi khởi tạo GPIO

### Ví dụ nâng cao:

1. **Multi-GPIO management**: Quản lý nhiều GPIO cùng lúc
2. **Pattern generation**: Tạo các pattern LED phức tạp
3. **System control**: Điều khiển relay, buzzer, status LED
4. **Emergency stop**: Chức năng dừng khẩn cấp an toàn

## Bài tập mở rộng

1. **Traffic light system**: Tạo hệ thống đèn giao thông với GPIO
2. **Security alarm**: Hệ thống báo động với cảm biến và GPIO
3. **Home automation**: Điều khiển thiết bị gia đình qua GPIO

## Lỗi thường gặp

```{admonition} Lỗi: GPIO không hoạt động
:class: warning

**Nguyên nhân**: Pin đã được sử dụng bởi chức năng khác

**Giải pháp**:
- Kiểm tra pin có bị conflict với I2C, SPI, UART không
- Sử dụng pin GPIO chuyên dụng (2, 4, 5, 18, 19, 21, 22, 23)
- Tránh sử dụng pin có chức năng đặc biệt (0, 1, 6-11)
- Kiểm tra schematic BeE Board
```

```{admonition} Lỗi: LED/Relay không sáng/hoạt động
:class: warning

**Nguyên nhân**: Dòng GPIO không đủ hoặc kết nối sai

**Giải pháp**:
- GPIO ESP32 chỉ cung cấp 12mA, cần transistor cho tải lớn
- Kiểm tra kết nối anode/cathode của LED
- Sử dụng điện trở hạn dòng phù hợp (220Ω-1kΩ)
- Với relay, cần driver transistor hoặc ULN2003
```

```{admonition} Lỗi: Trạng thái GPIO không ổn định
:class: warning

**Nguyên nhân**: Nhiễu điện hoặc floating pin

**Giải pháp**:
- Sử dụng pull-up/pull-down resistor
- Thêm capacitor lọc nhiễu (0.1μF)
- Kiểm tra nguồn điện ổn định
- Tránh dây dẫn quá dài
```

## Tài nguyên tham khảo

-   [ESP32 GPIO Guide](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html)
-   [MicroPython Pin Class](https://docs.micropython.org/en/latest/library/machine.Pin.html)
-   [BeE Board Pinout](https://beeblock.vn/docs/bee-ide/hardware/pinout)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
