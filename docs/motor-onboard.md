# MotorOnBoard - <PERSON><PERSON><PERSON> Đ<PERSON>hiển Motor Tích Hợp

## Giới thiệu

MotorOnBoard là module điều khiển motor DC và servo được tích hợp trực tiếp trên Be<PERSON>, sử dụng chip TB6612 cho motor DC và PWM của ESP32-S3 cho servo. Module này cung cấp khả năng điều khiển chính xác và mạnh mẽ cho các ứng dụng robot di chuyển.

Module bao gồm hai phần: Motor cho 2 motor DC và Servo cho 4 servo motor. Thiết kế tích hợp giúp tiết kiệm không gian và đơn giản hóa kết nối, phù hợp cho các dự án robot giáo dục và nghiên cứu.

**Ứng dụng thực tế:**

-   Robot di chuyển 2 bánh
-   <PERSON>e tự hành (AGV)
-   Robot arm với servo
-   <PERSON><PERSON> thống camera pan-tilt
-   Robot pet với chuyển động linh hoạt
-   Máy CNC mini
-   Hệ thống automation

## Thông số kỹ thuật

### Motor (Motor DC)

| Thông số          | Giá trị                      |
| ----------------- | ---------------------------- |
| Chip điều khiển   | TB6612FNG                    |
| Số kênh motor     | 2 (Motor A, Motor B)         |
| Điện áp motor     | 2.5V - 13.5V                 |
| Dòng tối đa       | 1.2A/kênh (liên tục)         |
| Dòng đỉnh         | 3.2A/kênh (10ms)             |
| Tần số PWM        | 3kHz                         |
| Công suất tối đa  | 100%                         |
| Chế độ điều khiển | Forward, Backward, Brake     |
| Pins Motor A      | AIN1(41), AIN2(42), PWMA(39) |
| Pins Motor B      | BIN1(35), BIN2(36), PWMB(40) |

### Servo (Servo Motor)

| Thông số           | Giá trị                        |
| ------------------ | ------------------------------ |
| Số kênh servo      | 4 (S1, S2, S3, S4)             |
| Tần số PWM         | 50Hz                           |
| Độ rộng xung       | 600-2400μs                     |
| Góc quay           | 0-270°                         |
| Điện áp servo      | 4.8V - 6V                      |
| Pins servo         | S1(37), S2(38), S3(47), S4(21) |
| Độ phân giải       | 16-bit PWM                     |
| Thời gian phản hồi | <20ms                          |

## Giao diện lập trình

### Khởi tạo Motor DC

```python
from MotorOnBoard import Motor

# Khởi tạo với pins mặc định
motors = Motor()

# Khởi tạo với pins tùy chỉnh
motors = Motor(
    ain1_pin=41, ain2_pin=42, pwma_pin=39,
    bin1_pin=35, bin2_pin=36, pwmb_pin=40
)
```

### Khởi tạo Servo

```python
from MotorOnBoard import Servo

# Khởi tạo với pins mặc định
servos = Servo()

# Khởi tạo với pins tùy chỉnh
servos = Servo(
    s1_pin=37, s2_pin=38, s3_pin=47, s4_pin=21
)
```

### Điều khiển Motor DC

#### `speed(motor_index, power)` - Thiết lập tốc độ motor

```python
# Motor A tiến với công suất 30%
motors.speed(0, 30)

# Motor B lùi với công suất 40%
motors.speed(1, -40)

# Dừng motor A
motors.speed(0, 0)
```

#### `brake(motor_index)` - Phanh motor

```python
# Phanh motor A
motors.brake(0)

# Phanh motor B
motors.brake(1)

# Phanh tất cả motor
motors.brake(0)
motors.brake(1)
```

### Điều khiển Servo

#### `position(index, degrees)` - Thiết lập vị trí servo

```python
# Servo 0 quay đến 90 độ
servos.position(0, degrees=90)

# Servo 1 quay đến 180 độ
servos.position(1, degrees=180)

# Servo 2 về vị trí 0 độ
servos.position(2, degrees=0)
```

#### `position()` với các đơn vị khác

```python
import math

# Sử dụng radian
servos.position(0, radians=math.pi/2)  # 90 độ

# Sử dụng microseconds
servos.position(1, us=1500)  # Vị trí giữa

# Sử dụng duty cycle trực tiếp
servos.position(2, duty=32768)  # 50% duty cycle
```

#### `release(index)` - Thả servo

```python
# Thả servo 0 (không giữ vị trí)
servos.release(0)

# Thả tất cả servo
for i in range(4):
    servos.release(i)
```

## Ví dụ Blockly

```blockly
when program starts:
    set motors to Motor
    set servos to Servo

    move servo 0 to 90 degrees
    move servo 1 to 0 degrees

forever:
    if button A pressed:
        set motor A speed to 40
        set motor B speed to 40
        wait 2 seconds
        brake all motors

    if button B pressed:
        set motor A speed to -30
        set motor B speed to 30
        wait 1 second
        brake all motors
```

## Ví dụ Python

### Ví dụ cơ bản - Robot di chuyển với servo arm

```python
from MotorOnBoard import Motor, Servo
from BeeBrain import bee
import time

def setup():
    """Khởi tạo hệ thống motor và servo"""
    global motors, servos

    try:
        # Khởi tạo motors và servos
        motors = Motor()
        servos = Servo()

        # Thiết lập vị trí ban đầu cho servo
        servos.position(0, degrees=90)   # Base servo
        servos.position(1, degrees=45)   # Shoulder servo
        servos.position(2, degrees=90)   # Elbow servo
        servos.position(3, degrees=0)    # Gripper servo

        # Hiển thị thông tin
        bee.oled.clear()
        bee.oled.write("Robot Ready", 15, 0)
        bee.oled.write("Motors: OK", 15, 15)
        bee.oled.write("Servos: OK", 15, 30)

        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
        bee.buzzer.play_song("C4 E4 G4")

        time.sleep(2)
        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("Init Error!", 15, 10)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
        return False

def move_forward(power=30, duration=1):
    """Di chuyển tiến"""
    motors.speed(0, power)  # Motor A
    motors.speed(1, power)  # Motor B
    time.sleep(duration)
    motors.brake(0)
    motors.brake(1)

def move_backward(power=30, duration=1):
    """Di chuyển lùi"""
    motors.speed(0, -power)  # Motor A
    motors.speed(1, -power)  # Motor B
    time.sleep(duration)
    motors.brake(0)
    motors.brake(1)

def turn_left(power=25, duration=1):
    """Rẽ trái"""
    motors.speed(0, -power)  # Motor A lùi
    motors.speed(1, power)   # Motor B tiến
    time.sleep(duration)
    motors.brake(0)
    motors.brake(1)

def turn_right(power=25, duration=1):
    """Rẽ phải"""
    motors.speed(0, power)   # Motor A tiến
    motors.speed(1, -power)  # Motor B lùi
    time.sleep(duration)
    motors.brake(0)
    motors.brake(1)

def servo_demo():
    """Demo chuyển động servo arm"""
    bee.oled.clear()
    bee.oled.write("Servo Demo", 15, 10)
    bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)

    # Sequence 1: Wave motion
    for angle in range(0, 181, 30):
        servos.position(0, degrees=angle)  # Base rotation
        time.sleep(0.3)

    for angle in range(180, -1, -30):
        servos.position(0, degrees=angle)
        time.sleep(0.3)

    # Sequence 2: Arm movement
    servos.position(1, degrees=90)   # Shoulder up
    time.sleep(0.5)
    servos.position(2, degrees=45)   # Elbow bend
    time.sleep(0.5)
    servos.position(3, degrees=90)   # Gripper close
    time.sleep(0.5)

    # Return to home position
    servos.position(1, degrees=45)
    servos.position(2, degrees=90)
    servos.position(3, degrees=0)
    time.sleep(1)

def robot_control():
    """Điều khiển robot bằng nút bấm"""
    while True:
        # Hiển thị trạng thái
        bee.oled.clear()
        bee.oled.write("Robot Control", 10, 0)
        bee.oled.write("A: Move/Servo", 10, 15)
        bee.oled.write("B: Turn", 25, 30)
        bee.oled.write("Both: Stop", 20, 45)

        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

        if bee.is_button_pressed(bee.BUTTON_A):
            # Chế độ di chuyển và servo
            bee.oled.clear()
            bee.oled.write("Moving Forward", 5, 10)
            bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)

            move_forward(35, 1.5)
            servo_demo()

        elif bee.is_button_pressed(bee.BUTTON_B):
            # Chế độ quay
            bee.oled.clear()
            bee.oled.write("Turning", 25, 10)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)

            turn_right(30, 1)
            time.sleep(0.5)
            turn_left(30, 1)

        elif bee.is_button_pressed(bee.BOTH_BUTTON):
            # Dừng tất cả
            motors.brake(0)
            motors.brake(1)
            for i in range(4):
                servos.release(i)

            bee.oled.clear()
            bee.oled.write("All Stopped", 15, 20)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            break

        time.sleep(0.1)

# Chạy chương trình
if setup():
    robot_control()
```

### Ví dụ nâng cao - Robot tự động với cảm biến

```python
from MotorOnBoard import Motor, Servo
from BeeBrain import bee
import time
import random

class AutonomousRobot:
    def __init__(self):
        self.motors = Motor()
        self.servos = Servo()

        # Thông số robot
        self.base_speed = 35
        self.turn_speed = 25
        self.scan_positions = [0, 45, 90, 135, 180]

        # Trạng thái robot
        self.current_state = "exploring"
        self.obstacle_detected = False
        self.scan_results = []

    def setup(self):
        """Khởi tạo robot tự động"""
        try:
            # Thiết lập servo scanner (giả sử servo 0 là cảm biến quét)
            self.servos.position(0, degrees=90)  # Hướng thẳng
            time.sleep(1)

            bee.oled.clear()
            bee.oled.write("Autonomous", 15, 0)
            bee.oled.write("Robot Ready", 15, 15)
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4 C5")

            time.sleep(2)
            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Setup Error!", 15, 10)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def read_distance_sensor(self):
        """Đọc cảm biến khoảng cách (giả lập)"""
        # Trong thực tế, đây sẽ là cảm biến siêu âm
        # Giả lập: random distance 10-100cm
        return random.randint(10, 100)

    def scan_environment(self):
        """Quét môi trường xung quanh"""
        self.scan_results = []

        bee.oled.clear()
        bee.oled.write("Scanning...", 15, 10)
        bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)

        for angle in self.scan_positions:
            # Quay servo scanner
            self.servos.position(0, degrees=angle)
            time.sleep(0.3)  # Chờ servo ổn định

            # Đọc khoảng cách
            distance = self.read_distance_sensor()
            self.scan_results.append((angle, distance))

            # Hiển thị kết quả quét
            bee.oled.clear()
            bee.oled.write(f"Scan: {angle}°", 15, 0)
            bee.oled.write(f"Dist: {distance}cm", 10, 15)

            time.sleep(0.2)

        # Trở về vị trí giữa
        self.servos.position(0, degrees=90)
        time.sleep(0.5)

    def analyze_scan_results(self):
        """Phân tích kết quả quét và quyết định hướng đi"""
        if not self.scan_results:
            return 90  # Mặc định đi thẳng

        # Tìm hướng có khoảng cách xa nhất
        best_direction = max(self.scan_results, key=lambda x: x[1])
        best_angle, best_distance = best_direction

        bee.oled.clear()
        bee.oled.write("Best Path:", 15, 0)
        bee.oled.write(f"Angle: {best_angle}°", 10, 15)
        bee.oled.write(f"Dist: {best_distance}cm", 10, 30)

        return best_angle, best_distance

    def navigate_to_direction(self, target_angle):
        """Điều hướng robot đến góc mục tiêu"""
        current_angle = 90  # Giả sử robot đang hướng thẳng
        angle_diff = target_angle - current_angle

        if abs(angle_diff) < 15:
            # Đi thẳng
            self.move_forward()
        elif angle_diff > 0:
            # Rẽ phải
            turn_time = abs(angle_diff) / 90.0  # Tỷ lệ thời gian quay
            self.turn_right(turn_time)
        else:
            # Rẽ trái
            turn_time = abs(angle_diff) / 90.0
            self.turn_left(turn_time)

    def move_forward(self, duration=1.5):
        """Di chuyển tiến với kiểm tra vật cản"""
        bee.oled.clear()
        bee.oled.write("Moving Forward", 5, 10)
        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

        self.motors.speed(0, self.base_speed)
        self.motors.speed(1, self.base_speed)

        # Di chuyển với kiểm tra liên tục
        start_time = time.time()
        while (time.time() - start_time) < duration:
            # Kiểm tra vật cản phía trước
            front_distance = self.read_distance_sensor()

            if front_distance < 20:  # Vật cản gần
                self.obstacle_detected = True
                break

            time.sleep(0.1)

        self.motors.brake(0)
        self.motors.brake(1)

    def turn_left(self, duration=1):
        """Rẽ trái"""
        bee.oled.clear()
        bee.oled.write("Turning Left", 10, 10)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)

        self.motors.speed(0, -self.turn_speed)
        self.motors.speed(1, self.turn_speed)
        time.sleep(duration)
        self.motors.brake(0)
        self.motors.brake(1)

    def turn_right(self, duration=1):
        """Rẽ phải"""
        bee.oled.clear()
        bee.oled.write("Turning Right", 5, 10)
        bee.neopixel.set_rgb(bee.LED1, 0, 255, 255)

        self.motors.speed(0, self.turn_speed)
        self.motors.speed(1, -self.turn_speed)
        time.sleep(duration)
        self.motors.brake(0)
        self.motors.brake(1)

    def avoid_obstacle(self):
        """Tránh vật cản"""
        bee.oled.clear()
        bee.oled.write("Obstacle!", 15, 0)
        bee.oled.write("Avoiding...", 10, 15)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
        bee.buzzer.play_song("A4:0.2 A4:0.2")

        # Lùi lại một chút
        self.motors.speed(0, -20)
        self.motors.speed(1, -20)
        time.sleep(0.5)
        self.motors.brake(0)
        self.motors.brake(1)

        # Quét môi trường để tìm đường đi
        self.scan_environment()
        best_angle, best_distance = self.analyze_scan_results()

        if best_distance > 30:  # Có đường đi an toàn
            self.navigate_to_direction(best_angle)
            self.obstacle_detected = False
        else:
            # Không có đường đi - quay 180 độ
            self.turn_right(2)  # Quay 180 độ
            self.obstacle_detected = False

    def exploration_behavior(self):
        """Hành vi khám phá tự do"""
        # Di chuyển ngẫu nhiên
        actions = [
            lambda: self.move_forward(2),
            lambda: self.turn_left(0.5),
            lambda: self.turn_right(0.5),
            lambda: self.scan_environment()
        ]

        action = random.choice(actions)
        action()

    def run(self):
        """Vòng lặp chính của robot tự động"""
        if not self.setup():
            return

        cycle_count = 0

        while True:
            try:
                cycle_count += 1

                # Hiển thị thông tin trạng thái
                bee.oled.clear()
                bee.oled.write(f"Cycle: {cycle_count}", 15, 0)
                bee.oled.write(f"State: {self.current_state}", 0, 15)

                if self.obstacle_detected:
                    self.avoid_obstacle()
                else:
                    # Hành vi bình thường
                    if cycle_count % 5 == 0:  # Quét môi trường mỗi 5 chu kỳ
                        self.scan_environment()
                        best_angle, best_distance = self.analyze_scan_results()

                        if best_distance > 50:
                            self.navigate_to_direction(best_angle)
                        else:
                            self.exploration_behavior()
                    else:
                        self.move_forward(1)

                # Kiểm tra nút dừng
                if bee.is_button_pressed(bee.BUTTON_B):
                    break

                time.sleep(0.5)

            except KeyboardInterrupt:
                break

        # Dừng robot
        self.motors.brake(0)
        self.motors.brake(1)
        for i in range(4):
            self.servos.release(i)

        bee.oled.clear()
        bee.oled.write("Robot", 25, 10)
        bee.oled.write("Stopped", 20, 25)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)

# Chạy robot tự động
if __name__ == "__main__":
    robot = AutonomousRobot()
    robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Motor control**: Điều khiển 2 motor DC với các hàm di chuyển cơ bản
2. **Servo arm**: Điều khiển 4 servo tạo thành cánh tay robot
3. **Coordinated movement**: Kết hợp chuyển động motor và servo
4. **User interaction**: Điều khiển bằng nút bấm với feedback

### Ví dụ nâng cao:

1. **Autonomous navigation**: Robot tự động điều hướng và tránh vật cản
2. **Environment scanning**: Quét môi trường bằng servo scanner
3. **Decision making**: Phân tích dữ liệu và quyết định hướng đi
4. **State machine**: Quản lý trạng thái robot (exploring, avoiding, etc.)

## Bài tập mở rộng

1. **Line following robot**: Kết hợp với cảm biến dò đường để tạo robot theo đường
2. **Remote control robot**: Điều khiển từ xa qua WiFi/Bluetooth
3. **Pick and place robot**: Robot có thể nhặt và đặt vật dụng bằng servo arm

## Lỗi thường gặp

```{admonition} Lỗi: Motor không quay hoặc yếu
:class: warning

**Nguyên nhân**: Nguồn điện không đủ hoặc kết nối lỏng

**Giải pháp**:
- Kiểm tra điện áp pin (>7V cho motor 6V)
- Đảm bảo kết nối motor chắc chắn
- Kiểm tra driver TB6612 có bị nóng không
- Thử giảm công suất motor xuống <50%
```

```{admonition} Lỗi: Servo rung hoặc không giữ vị trí
:class: warning

**Nguyên nhân**: Tín hiệu PWM không ổn định hoặc nguồn yếu

**Giải pháp**:
- Kiểm tra nguồn 5V cho servo
- Đảm bảo tần số PWM = 50Hz
- Kiểm tra độ rộng xung (600-2400μs)
- Tránh cập nhật vị trí quá nhanh
```

```{admonition} Lỗi: Robot di chuyển không thẳng
:class: warning

**Nguyên nhân**: Motor không đồng bộ hoặc bánh xe khác nhau

**Giải pháp**:
- Hiệu chỉnh tốc độ từng motor riêng biệt
- Kiểm tra bánh xe có cùng kích thước
- Đảm bảo trục motor thẳng và song song
- Sử dụng encoder để feedback tốc độ
```

## Tài nguyên tham khảo

-   [TB6612FNG Datasheet](https://www.sparkfun.com/datasheets/Robotics/TB6612FNG.pdf)
-   [Servo Motor Control Guide](https://beeblock.vn/docs/bee-ide/tutorials/servo-control)
-   [Robot Kinematics Tutorial](https://www.robotics.org/joseph-elias-node/kinematics.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
