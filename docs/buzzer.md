# Buzzer - <PERSON><PERSON>le Âm <PERSON>

## Giới thiệu

Module Buzzer cung cấp giao diện tạo âm thanh và phát nhạc cho BeE Board sử dụng PWM. Module hỗ trợ phát các nốt nhạc đơn lẻ, chuỗi nốt nhạc và điều chỉnh âm lượng, tạo ra các hiệu ứng âm thanh đa dạng cho dự án.

Buzzer là thiết bị tạo âm thanh đơn giản nhưng hiệu quả, đư<PERSON>c sử dụng rộng rãi trong các ứng dụng báo hiệu, cảnh báo và giải trí. BeE Board có buzzer tích hợp sẵn và có thể kết nối thêm loa ngoài để tăng âm lượng.

**Ứng dụng thực tế:**

-   B<PERSON>o hiệu trạng thái hệ thống
-   <PERSON><PERSON>nh báo và báo động
-   <PERSON><PERSON><PERSON> nhạc chuông và hiệu ứng âm thanh
-   <PERSON><PERSON><PERSON> chơi âm nhạc
-   <PERSON>ản hồi âm thanh cho người dùng
-   Đ<PERSON>ng hồ báo thức

## Thông số kỹ thuật

| Thông số          | Giá trị                             |
| ----------------- | ----------------------------------- |
| Loại tín hiệu     | PWM (Pulse Width Modulation)        |
| Dải tần số        | 20Hz - 20kHz                        |
| Điện áp hoạt động | 3.3V                                |
| Dòng tiêu thụ     | ~20mA                               |
| Âm lượng          | 0-100% (điều chỉnh bằng duty cycle) |
| Độ phân giải      | 16-bit PWM                          |
| Nốt nhạc hỗ trợ   | C4 - C7 (261Hz - 2093Hz)            |
| Giao tiếp         | GPIO PWM                            |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from Buzzer import Buzzer

# Khởi tạo buzzer với buzzer tích hợp
buzzer = bee.buzzer

# Khởi tạo với âm lượng tùy chỉnh
buzzer_loud = Buzzer(port=bee.PORT1, volume=75)
```

### Các phương thức chính

#### `play_tone(frequency)` - Phát tần số

```python
# Phát nốt A4 (440Hz)
buzzer.play_tone(440)
```

#### `set_volume(volume)` - Đặt âm lượng

```python
# Đặt âm lượng 50%
buzzer.set_volume(50)

# Âm lượng tối đa
buzzer.set_volume(100)
```

#### `be_quiet()` - Tắt âm thanh

```python
# Dừng phát âm
buzzer.be_quiet()
```

#### `play_song(song, delay)` - Phát chuỗi nốt nhạc

```python
# Phát giai điệu đơn giản
buzzer.play_song("C4 E4 G4 C5")

# Phát với thời gian tùy chỉnh
buzzer.play_song("C4:1.0 E4:0.5 G4:2.0", origin_delay=0.3)
```

### Nốt nhạc có sẵn

Module hỗ trợ các nốt nhạc từ C4 đến C7:

-   **C4-B4**: 261Hz - 493Hz (quãng thấp)
-   **C5-B5**: 523Hz - 988Hz (quãng trung)
-   **C6-B6**: 1046Hz - 1976Hz (quãng cao)
-   **C7**: 2093Hz (nốt cao nhất)

## Ví dụ Blockly

```blockly
when program starts:
    set buzzer volume to 70%
    play tone C4 for 0.5 seconds
    play tone E4 for 0.5 seconds
    play tone G4 for 1 second
    stop buzzer

when button A pressed:
    play song "C4 D4 E4 F4 G4"

when sensor detects motion:
    play alarm sound "A5:0.2 SIL:0.1 A5:0.2 SIL:0.1 A5:0.2"
```

## Ví dụ Python

### Ví dụ cơ bản - Chuông cửa

```python
from BeeBrain import bee
from Buzzer import Buzzer
from Button import Button
import time

# Khởi tạo
buzzer = Buzzer(port=bee.PORT1, volume=60)
doorbell = bee.buttonA  # Button A

# Giai điệu chuông cửa
doorbell_melody = "C5:0.3 E5:0.3 G5:0.6 C5:0.3 G4:0.6"

def ring_doorbell(pin):
    """Phát nhạc chuông cửa"""
    print("Ding dong!")
    buzzer.play_song(doorbell_melody)

# Đăng ký sự kiện
doorbell.on_press(ring_doorbell)

print("Chuông cửa đã sẵn sàng. Nhấn Button A để rung chuông.")

while True:
    time.sleep(0.1)
```

### Ví dụ nâng cao - Hệ thống báo động thông minh

```python
from BeeBrain import bee
from Buzzer import Buzzer
from Ultrasonic import Ultrasonic
from Oled import Oled
import time

# Khởi tạo các module
buzzer = Buzzer(port=bee.PORT1, volume=80)
sensor = Ultrasonic(port=bee.PORT2)
oled = Oled(port=bee.PORT3)

# Các loại âm thanh cảnh báo
ALERT_SOUNDS = {
    "warning": "A4:0.2 SIL:0.1 A4:0.2 SIL:0.1",
    "danger": "A5:0.1 SIL:0.05 A5:0.1 SIL:0.05 A5:0.1 SIL:0.05",
    "safe": "C4:0.3 E4:0.3 G4:0.5",
    "startup": "C4 D4 E4 F4 G4 A4 B4 C5"
}

def play_startup_sound():
    """Phát âm thanh khởi động"""
    buzzer.play_song(ALERT_SOUNDS["startup"], 0.2)

def display_status(distance, status):
    """Hiển thị trạng thái trên OLED"""
    oled.clear()
    oled.write("Security System", 10, 0)
    oled.write(f"Distance: {distance}cm", 0, 20)
    oled.write(f"Status: {status}", 0, 35)

    # Vẽ thanh cảnh báo
    if status == "DANGER":
        oled.draw_rect(0, 50, 128, 10, 1)
    elif status == "WARNING":
        oled.draw_rect(0, 50, 64, 10, 1)

    oled.show()

def security_system():
    """Hệ thống báo động chính"""
    play_startup_sound()

    while True:
        distance = sensor.distance_cm()

        if distance == -1:
            # Không phát hiện được
            status = "NO SIGNAL"
            display_status(0, status)

        elif distance < 20:
            # Nguy hiểm - quá gần
            status = "DANGER"
            display_status(distance, status)
            buzzer.play_song(ALERT_SOUNDS["danger"])

        elif distance < 50:
            # Cảnh báo - gần
            status = "WARNING"
            display_status(distance, status)
            buzzer.play_song(ALERT_SOUNDS["warning"])

        else:
            # An toàn
            status = "SAFE"
            display_status(distance, status)

        time.sleep(0.5)

def musical_scale_demo():
    """Demo các nốt nhạc"""
    notes = ["C4", "D4", "E4", "F4", "G4", "A4", "B4", "C5"]

    print("Phát thang âm C major...")
    for note in notes:
        buzzer.play_tone(buzzer.TONES[note])
        time.sleep(0.5)

    buzzer.be_quiet()
    time.sleep(1)

    # Phát ngược lại
    print("Phát ngược...")
    for note in reversed(notes):
        buzzer.play_tone(buzzer.TONES[note])
        time.sleep(0.3)

    buzzer.be_quiet()

def create_alarm_pattern():
    """Tạo pattern báo động phức tạp"""
    patterns = [
        "A5:0.1 SIL:0.1 A5:0.1 SIL:0.1 A5:0.1 SIL:0.5",  # Beep nhanh
        "C4:0.5 SIL:0.2 G4:0.5 SIL:0.2 C5:0.5 SIL:0.5",   # Tăng dần
        "A4:0.2 F4:0.2 A4:0.2 F4:0.2 A4:0.2 SIL:0.5"      # Xen kẽ
    ]

    for i, pattern in enumerate(patterns):
        print(f"Pattern {i+1}:")
        buzzer.play_song(pattern)
        time.sleep(1)

# Chạy demo
if __name__ == "__main__":
    print("=== Buzzer Demo ===")

    # Demo thang âm
    musical_scale_demo()

    # Demo patterns
    create_alarm_pattern()

    # Chạy hệ thống báo động
    print("Khởi động hệ thống báo động...")
    security_system()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Khởi tạo**: Tạo đối tượng Buzzer và Button
2. **Melody**: Định nghĩa chuỗi nốt nhạc với thời gian
3. **Callback**: Hàm xử lý sự kiện nhấn nút
4. **Event loop**: Vòng lặp chờ sự kiện

### Ví dụ nâng cao:

1. **Multi-module**: Kết hợp buzzer, ultrasonic và OLED
2. **Sound library**: Tạo thư viện âm thanh cho các tình huống khác nhau
3. **Status display**: Hiển thị trạng thái trực quan trên OLED
4. **Pattern generation**: Tạo các pattern âm thanh phức tạp

## Bài tập mở rộng

1. **Piano điện tử**: Tạo piano mini với các nút bấm tương ứng với các nốt nhạc
2. **Trò chơi âm nhạc**: Tạo game "Simon Says" với sequence âm thanh
3. **Đồng hồ báo thức**: Hệ thống báo thức với nhiều giai điệu khác nhau

## Lỗi thường gặp

```{admonition} Lỗi: Không có âm thanh
:class: warning

**Nguyên nhân**: Pin PWM không được cấu hình đúng hoặc buzzer hỏng

**Giải pháp**:
- Kiểm tra kết nối pin buzzer
- Đảm bảo pin hỗ trợ PWM
- Thử với pin khác
- Kiểm tra âm lượng (volume > 0)
```

```{admonition} Lỗi: Âm thanh bị méo hoặc không đúng tần số
:class: warning

**Nguyên nhân**: Tần số PWM không chính xác hoặc duty cycle sai

**Giải pháp**:
- Sử dụng tần số trong khoảng hỗ trợ (20Hz-20kHz)
- Kiểm tra giá trị duty cycle
- Tránh nhiễu từ các module khác
- Sử dụng nguồn ổn định
```

```{admonition} Lỗi: Nhạc phát không đúng thời gian
:class: warning

**Nguyên nhân**: Delay không chính xác hoặc bị interrupt

**Giải pháp**:
- Sử dụng `time.sleep()` thay vì `time.sleep_ms()`
- Tránh interrupt trong khi phát nhạc
- Kiểm tra format chuỗi nhạc
- Đảm bảo gọi `be_quiet()` sau khi phát xong
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [PWM Theory and Applications](https://learn.adafruit.com/adafruit-arduino-lesson-3-rgb-leds/theory-pwm)
-   [Musical Note Frequencies](https://pages.mtu.edu/~suits/notefreqs.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
