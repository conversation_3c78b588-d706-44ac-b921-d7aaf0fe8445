# Button - <PERSON><PERSON><PERSON> Bấm

## Giới thiệu

Module Button cung cấp giao diện điều khiển nút bấm cho BeE Board với khả năng chống nhiễu (debounce) và xử lý sự kiện. Module hỗ trợ phát hiện các sự kiện nhấn, thả và nhấn giữ với callback functions.

N<PERSON>t bấm là một trong những thành phần cơ bản nhất trong lập trình <PERSON>hún<PERSON>, được sử dụng để tương tác với người dùng. Tr<PERSON>n BeE Board, có 2 nút bấm tích hợp sẵn (Button A và Button B) và có thể kết nối thêm các nút bấm ngoài qua các cổng PORT.

**Ứng dụng thực tế:**

-   <PERSON><PERSON><PERSON><PERSON>hiể<PERSON> LED, độ<PERSON> cơ, buzzer
-   Tạo menu điều hướng
-   <PERSON><PERSON><PERSON> chơi tương tác
-   <PERSON><PERSON> thống báo động
-   <PERSON><PERSON><PERSON><PERSON> khiển robot

## Thông số kỹ thuật

| Thông số           | Giá trị                       |
| ------------------ | ----------------------------- |
| Điện áp hoạt động  | 3.3V                          |
| Loại tín hiệu      | Digital (HIGH/LOW)            |
| Chế độ pull        | PULL_UP (mặc định), PULL_DOWN |
| Thời gian debounce | 200ms (có thể tùy chỉnh)      |
| Thời gian nhấn giữ | 1000ms (có thể tùy chỉnh)     |
| Tương thích        | GPIO pins của ESP32           |
| Giao tiếp          | Interrupt-based               |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from Button import Button

# Sử dụng nút tích hợp trên board
button_a = bee.buttonA  # Button A
button_b = bee.buttonB  # Button B

# Sử dụng nút ngoài với pull-down
external_button = Button(port=bee.PORT3, Button.PULL_DOWN, debounce_ms=150)
```

### Các phương thức chính

#### `is_pressed()` - Kiểm tra trạng thái nhấn

```python
if button_a.is_pressed():
    print("Nút A đang được nhấn")
```

#### `is_released()` - Kiểm tra trạng thái thả

```python
if button_a.is_released():
    print("Nút A đang được thả")
```

#### `on_press(callback)` - Đăng ký sự kiện nhấn

```python
def handle_press(pin):
    print("Nút được nhấn!")

button_a.on_press(handle_press)
```

#### `on_release(callback)` - Đăng ký sự kiện thả

```python
def handle_release(pin):
    print("Nút được thả!")

button_a.on_release(handle_release)
```

#### `on_long_press(callback, duration_ms)` - Đăng ký sự kiện nhấn giữ

```python
def handle_long_press(pin):
    print("Nút được nhấn giữ!")

button_a.on_long_press(handle_long_press, 2000)  # 2 giây
```

## Ví dụ Blockly

```blockly
when button A pressed:
    set LED to ON

when button A released:
    set LED to OFF

when button B long pressed (2 seconds):
    play sound "beep"
    show text "Long press detected!"
```

## Ví dụ Python

### Ví dụ cơ bản - Điều khiển LED

```python
from BeeBrain import bee
from Button import Button
from machine import Pin

# Khởi tạo nút và LED
button = bee.buttonA  # Button A
led = bee.led1 # LED 1

# Hàm xử lý khi nhấn nút
def toggle_led(pin):
    led.toggle()
    print("LED1 toggled!")

# Đăng ký sự kiện
button.on_press(toggle_led)

# Vòng lặp chính
while True:
    pass  # Chờ sự kiện interrupt
```

### Ví dụ nâng cao - Menu điều hướng

```python
from BeeBrain import bee
from Button import Button
from Oled import Oled
import time

# Khởi tạo
button_up = Button(port=bee.PORT1)
button_down = Button(port=bee.PORT2)
oled = Oled(port=bee.PORT3)

menu_items = ["LED Control", "Motor Control", "Sensor Read", "Settings"]
current_item = 0

def show_menu():
    oled.clear()
    for i, item in enumerate(menu_items):
        prefix = "> " if i == current_item else "  "
        oled.write(f"{prefix}{item}", 0, i * 10)

def menu_up(pin):
    global current_item
    current_item = (current_item - 1) % len(menu_items)
    show_menu()

def menu_down(pin):
    global current_item
    current_item = (current_item + 1) % len(menu_items)
    show_menu()

def select_item(pin):
    selected = menu_items[current_item]
    oled.clear()
    oled.write(f"Selected: {selected}", 0, 20)
    time.sleep(2)
    show_menu()

# Đăng ký sự kiện
button_up.on_press(menu_up)
button_down.on_press(menu_down)
button_up.on_long_press(select_item, 1000)

# Hiển thị menu ban đầu
show_menu()

while True:
    pass
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Khởi tạo**: Tạo đối tượng Button với pin 36 (Button A tích hợp)
2. **Callback function**: Định nghĩa hàm `toggle_led` để đảo trạng thái LED
3. **Đăng ký sự kiện**: Sử dụng `on_press()` để gắn callback với sự kiện nhấn
4. **Vòng lặp**: Chương trình chờ sự kiện interrupt, không cần polling

### Ví dụ nâng cao:

1. **Menu system**: Tạo danh sách menu items và biến theo dõi vị trí hiện tại
2. **Navigation**: Sử dụng 2 nút để điều hướng lên/xuống trong menu
3. **Selection**: Nhấn giữ để chọn item hiện tại
4. **Display update**: Cập nhật màn hình OLED mỗi khi có thay đổi

## Bài tập mở rộng

1. **Đếm số lần nhấn**: Tạo chương trình đếm và hiển thị số lần nhấn nút trên OLED
2. **Trò chơi phản xạ**: Tạo game đo thời gian phản xạ - LED sáng ngẫu nhiên, người chơi phải nhấn nút nhanh nhất
3. **Hệ thống mật khẩu**: Tạo khóa số đơn giản bằng cách nhấn nút theo sequence cụ thể

## Lỗi thường gặp

```{admonition} Lỗi: Nút không phản hồi
:class: warning

**Nguyên nhân**: Cấu hình pull resistor không đúng hoặc debounce time quá ngắn

**Giải pháp**:
- Kiểm tra cấu hình PULL_UP/PULL_DOWN
- Tăng thời gian debounce lên 200-300ms
- Kiểm tra kết nối phần cứng
```

```{admonition} Lỗi: Sự kiện bị gọi nhiều lần
:class: warning

**Nguyên nhân**: Nhiễu tín hiệu hoặc debounce không hiệu quả

**Giải pháp**:
- Tăng thời gian debounce
- Sử dụng tụ lọc phần cứng (10nF-100nF)
- Kiểm tra chất lượng nút bấm
```

```{admonition} Lỗi: Long press không hoạt động
:class: warning

**Nguyên nhân**: Thời gian long press quá ngắn hoặc callback không được đăng ký đúng

**Giải pháp**:
- Đặt thời gian long press >= 1000ms
- Đảm bảo callback function có đúng signature
- Kiểm tra logic xử lý trong callback
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [MicroPython Pin Documentation](https://docs.micropython.org/en/latest/library/machine.Pin.html)
-   [ESP32 GPIO Reference](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
