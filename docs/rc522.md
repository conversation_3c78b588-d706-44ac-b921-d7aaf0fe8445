# RC522 - <PERSON><PERSON><PERSON> Thẻ RFID

## Giới thiệu

RC522 là module đọc thẻ RFID/NFC sử dụng chip RC522, hỗ trợ giao tiếp I2C và có thể đọc các loại thẻ MIFARE Classic và NTAG. Mo<PERSON>le này cho phép nhận diện thẻ từ xa, lưu trữ danh sách thẻ và thực hiện các chức năng kiểm soát truy cập.

Module tích hợp antenna 13.56MHz và có thể đọc thẻ ở khoảng cách lên đến 5cm. <PERSON><PERSON><PERSON> là giải pháp lý tưởng cho các hệ thống bảo mật, điểm danh, thanh toán và quản lý tài sản.

**Ứng dụng thực tế:**

-   Hệ thống kiểm soát ra vào
-   <PERSON><PERSON><PERSON> chấm công tự động
-   <PERSON>ệ thống thanh toán không tiếp xúc
-   <PERSON>u<PERSON><PERSON> lý thư viện/tài sản
-   <PERSON><PERSON><PERSON> chơi tương tác với thẻ
-   <PERSON><PERSON> thống điểm danh học sinh
-   Robot pet với nhận diện chủ nhân

## Thông số kỹ thuật

| Thông số             | Giá trị                         |
| -------------------- | ------------------------------- |
| Chip điều khiển      | RC522                           |
| Tần số hoạt động     | 13.56MHz                        |
| Giao tiếp            | I2C                             |
| Địa chỉ I2C mặc định | 0x2C                            |
| Khoảng cách đọc      | 0-50mm                          |
| Loại thẻ hỗ trợ      | MIFARE Classic, NTAG213/215/216 |
| Tốc độ truyền        | 106 kbit/s                      |
| Điện áp hoạt động    | 3.3V                            |
| Dòng tiêu thụ        | 13-26mA                         |
| Thời gian đọc thẻ    | <100ms                          |
| Antenna              | Tích hợp PCB antenna            |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from RC522 import RC522

# Khởi tạo với địa chỉ mặc định
rfid = RC522(port=bee.PORT1)

# Khởi tạo với địa chỉ tùy chỉnh
rfid = RC522(port=bee.PORT2, address=0x2D)

# Khởi tạo với ASW switch
rfid = RC522(port=bee.PORT1, asw=[0, 1])  # Địa chỉ = 0x2C + 0 + 2*1 = 0x2E
```

### Đọc thẻ cơ bản

#### `readID(detail=False)` - Đọc ID thẻ

```python
# Đọc ID dạng chuỗi
card_id = rfid.readID()
print(f"Card ID: {card_id}")

# Đọc thông tin chi tiết
card_info = rfid.readID(detail=True)
print(f"Success: {card_info['success']}")
print(f"ID: {card_info['id_formatted']}")
print(f"Type: {card_info['type']}")
```

#### `tagPresent()` - Kiểm tra có thẻ không

```python
if rfid.tagPresent():
    print("Có thẻ trong vùng đọc")
    card_id = rfid.readID()
else:
    print("Không có thẻ")
```

#### `scan_card()` - Quét thẻ nhanh

```python
card_id = rfid.scan_card()
if card_id:
    print(f"Quét được thẻ: {card_id}")
else:
    print("Không có thẻ")
```

### Quản lý danh sách thẻ

#### `load_list(list_name)` - Tải danh sách thẻ

```python
# Tải danh sách từ file
authorized_cards = rfid.load_list("authorized")
print(f"Có {len(authorized_cards)} thẻ được phép")
```

#### `scan_and_add_card(list_name)` - Quét và thêm thẻ

```python
# Thêm thẻ vào danh sách
if rfid.scan_and_add_card("authorized"):
    print("Đã thêm thẻ mới")
else:
    print("Không có thẻ hoặc thẻ đã tồn tại")
```

#### `scan_and_check(list_name)` - Kiểm tra thẻ có trong danh sách

```python
# Kiểm tra thẻ có được phép không
if rfid.scan_and_check("authorized"):
    print("Thẻ hợp lệ - Cho phép truy cập")
else:
    print("Thẻ không hợp lệ - Từ chối truy cập")
```

#### `scan_and_remove_card(list_name)` - Xóa thẻ khỏi danh sách

```python
# Xóa thẻ khỏi danh sách
rfid.scan_and_remove_card("authorized")
print("Đã xóa thẻ khỏi danh sách")
```

#### `clear_list(list_name)` - Xóa toàn bộ danh sách

```python
# Xóa tất cả thẻ trong danh sách
rfid.clear_list("authorized")
print("Đã xóa toàn bộ danh sách")
```

## Ví dụ Blockly

```blockly
when program starts:
    set rfid_reader to RC522 at PORT1
    load authorized_cards list
    show "RFID Ready" on OLED

forever:
    if card is present:
        set card_id to scan card

        if card_id is in authorized_cards:
            show "ACCESS GRANTED" on OLED
            set LED to GREEN
            play success sound
            wait 2 seconds
        else:
            show "ACCESS DENIED" on OLED
            set LED to RED
            play error sound
            wait 1 second

    when button A pressed:
        show "Add new card..." on OLED
        if scan and add card to authorized_cards:
            show "Card added!" on OLED
            play confirmation sound
```

## Ví dụ Python

### Ví dụ cơ bản - Hệ thống kiểm soát truy cập

```python
from RC522 import RC522
from BeeBrain import bee
import time

def setup():
    """Khởi tạo hệ thống RFID"""
    global rfid

    try:
        # Khởi tạo RFID reader
        rfid = RC522(port=bee.PORT1)

        # Tải danh sách thẻ được phép
        authorized_cards = rfid.load_list("authorized")

        # Hiển thị thông tin khởi tạo
        bee.oled.clear()
        bee.oled.write("RFID Access", 15, 0)
        bee.oled.write("Control System", 5, 15)
        bee.oled.write(f"Cards: {len(authorized_cards)}", 15, 30)

        bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)  # LED xanh dương
        bee.buzzer.play_song("C4 E4 G4")

        time.sleep(2)
        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("RFID Error!", 15, 10)
        bee.oled.write(str(e)[:16], 0, 25)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # LED đỏ
        return False

def access_control():
    """Vòng lặp kiểm soát truy cập"""
    last_card_time = 0
    card_cooldown = 2  # 2 giây cooldown giữa các lần đọc

    while True:
        current_time = time.time()

        # Hiển thị trạng thái chờ
        bee.oled.clear()
        bee.oled.write("Please scan", 15, 10)
        bee.oled.write("your card", 20, 25)
        bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # LED vàng

        # Kiểm tra có thẻ không
        if rfid.tagPresent() and (current_time - last_card_time) > card_cooldown:
            card_id = rfid.readID()

            if card_id:
                last_card_time = current_time

                # Kiểm tra thẻ có trong danh sách không
                if rfid.scan_and_check("authorized"):
                    # Truy cập được phép
                    bee.oled.clear()
                    bee.oled.write("ACCESS", 25, 5)
                    bee.oled.write("GRANTED", 20, 20)
                    bee.oled.write(card_id[:12], 5, 35)  # Hiển thị ID (12 ký tự đầu)

                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # LED xanh
                    bee.buzzer.play_song("C4 E4 G4 C5")

                    # Mô phỏng mở cửa
                    if hasattr(bee, 'servo'):
                        bee.servo.position(0, 90)  # Mở cửa
                        time.sleep(3)
                        bee.servo.position(0, 0)   # Đóng cửa

                    time.sleep(2)

                else:
                    # Truy cập bị từ chối
                    bee.oled.clear()
                    bee.oled.write("ACCESS", 25, 5)
                    bee.oled.write("DENIED", 25, 20)
                    bee.oled.write("Unknown Card", 10, 35)

                    bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # LED đỏ
                    bee.buzzer.play_song("A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2")

                    time.sleep(2)

        # Kiểm tra nút thêm thẻ mới
        if bee.is_button_pressed(bee.BUTTON_A):
            add_new_card()

        # Kiểm tra nút xóa thẻ
        if bee.is_button_pressed(bee.BUTTON_B):
            remove_card()

        time.sleep(0.1)

def add_new_card():
    """Thêm thẻ mới vào danh sách"""
    bee.oled.clear()
    bee.oled.write("ADD NEW CARD", 10, 0)
    bee.oled.write("Scan card to", 10, 15)
    bee.oled.write("add...", 30, 30)

    bee.neopixel.set_rgb(bee.LED1, 0, 255, 255)  # LED cyan

    timeout = 10  # 10 giây timeout
    start_time = time.time()

    while (time.time() - start_time) < timeout:
        if rfid.scan_and_add_card("authorized"):
            # Thêm thành công
            bee.oled.clear()
            bee.oled.write("CARD ADDED", 15, 10)
            bee.oled.write("Successfully!", 5, 25)

            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # LED xanh
            bee.buzzer.play_song("C4 E4 G4 C5 G4")

            time.sleep(2)
            return

        time.sleep(0.1)

    # Timeout
    bee.oled.clear()
    bee.oled.write("TIMEOUT", 25, 10)
    bee.oled.write("No card added", 5, 25)
    bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # LED vàng
    time.sleep(1)

def remove_card():
    """Xóa thẻ khỏi danh sách"""
    bee.oled.clear()
    bee.oled.write("REMOVE CARD", 15, 0)
    bee.oled.write("Scan card to", 10, 15)
    bee.oled.write("remove...", 20, 30)

    bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)  # LED tím

    timeout = 10  # 10 giây timeout
    start_time = time.time()

    while (time.time() - start_time) < timeout:
        if rfid.tagPresent():
            card_id = rfid.readID()
            if card_id and rfid.scan_and_check("authorized"):
                # Thẻ có trong danh sách - xóa
                rfid.scan_and_remove_card("authorized")

                bee.oled.clear()
                bee.oled.write("CARD REMOVED", 10, 10)
                bee.oled.write("Successfully!", 5, 25)

                bee.neopixel.set_rgb(bee.LED1, 255, 165, 0)  # LED cam
                bee.buzzer.play_song("G4 E4 C4")

                time.sleep(2)
                return
            elif card_id:
                # Thẻ không có trong danh sách
                bee.oled.clear()
                bee.oled.write("CARD NOT", 20, 10)
                bee.oled.write("IN LIST", 25, 25)
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # LED đỏ
                time.sleep(1)

        time.sleep(0.1)

    # Timeout
    bee.oled.clear()
    bee.oled.write("TIMEOUT", 25, 10)
    bee.oled.write("No card removed", 0, 25)
    bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # LED vàng
    time.sleep(1)

# Chạy chương trình
if setup():
    access_control()
```

### Ví dụ nâng cao - Hệ thống điểm danh thông minh

```python
from RC522 import RC522
from BeeBrain import bee
import time
import json

class AttendanceSystem:
    def __init__(self):
        self.rfid = RC522(port=bee.PORT1)
        self.students = {}
        self.attendance_log = []
        self.session_active = False

    def setup(self):
        """Khởi tạo hệ thống điểm danh"""
        try:
            # Tải danh sách học sinh
            self.load_students()

            bee.oled.clear()
            bee.oled.write("Attendance", 15, 0)
            bee.oled.write("System Ready", 10, 15)
            bee.oled.write(f"Students: {len(self.students)}", 10, 30)

            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4 C5")

            time.sleep(2)
            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("System Error!", 10, 10)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def load_students(self):
        """Tải danh sách học sinh từ file"""
        try:
            with open("students.json", "r") as f:
                self.students = json.load(f)
        except:
            # File không tồn tại - tạo danh sách mẫu
            self.students = {
                "04:A3:B2:C1": {"name": "Nguyen Van A", "class": "10A1"},
                "05:B4:C3:D2": {"name": "Tran Thi B", "class": "10A1"},
                "06:C5:D4:E3": {"name": "Le Van C", "class": "10A2"}
            }
            self.save_students()

    def save_students(self):
        """Lưu danh sách học sinh"""
        try:
            with open("students.json", "w") as f:
                json.dump(self.students, f)
        except:
            pass

    def save_attendance_log(self):
        """Lưu log điểm danh"""
        try:
            filename = f"attendance_{time.strftime('%Y%m%d')}.json"
            with open(filename, "w") as f:
                json.dump(self.attendance_log, f)
        except:
            pass

    def start_session(self):
        """Bắt đầu phiên điểm danh"""
        self.session_active = True
        self.attendance_log = []

        bee.oled.clear()
        bee.oled.write("SESSION", 25, 0)
        bee.oled.write("STARTED", 25, 15)
        bee.oled.write("Scan your card", 5, 30)

        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
        bee.buzzer.play_song("C4 E4 G4")

    def end_session(self):
        """Kết thúc phiên điểm danh"""
        self.session_active = False
        self.save_attendance_log()

        present_count = len(self.attendance_log)
        total_count = len(self.students)

        bee.oled.clear()
        bee.oled.write("SESSION", 25, 0)
        bee.oled.write("ENDED", 30, 15)
        bee.oled.write(f"Present: {present_count}/{total_count}", 5, 30)

        bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)
        bee.buzzer.play_song("G4 E4 C4")

        time.sleep(3)

    def check_attendance(self, card_id):
        """Kiểm tra và ghi nhận điểm danh"""
        if card_id in self.students:
            student = self.students[card_id]

            # Kiểm tra đã điểm danh chưa
            already_present = any(log['card_id'] == card_id for log in self.attendance_log)

            if not already_present:
                # Ghi nhận điểm danh
                attendance_record = {
                    'card_id': card_id,
                    'name': student['name'],
                    'class': student['class'],
                    'time': time.strftime('%H:%M:%S'),
                    'timestamp': time.time()
                }
                self.attendance_log.append(attendance_record)

                # Hiển thị thông tin
                bee.oled.clear()
                bee.oled.write("PRESENT", 25, 0)
                bee.oled.write(student['name'][:16], 0, 15)
                bee.oled.write(student['class'], 30, 30)
                bee.oled.write(f"Time: {attendance_record['time']}", 0, 45)

                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # Xanh
                bee.buzzer.play_song("C5 E5")

                return True
            else:
                # Đã điểm danh rồi
                bee.oled.clear()
                bee.oled.write("ALREADY", 25, 0)
                bee.oled.write("PRESENT", 25, 15)
                bee.oled.write(student['name'][:16], 0, 30)

                bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng
                bee.buzzer.play_song("E4 E4")

                return False
        else:
            # Thẻ không hợp lệ
            bee.oled.clear()
            bee.oled.write("UNKNOWN", 25, 0)
            bee.oled.write("STUDENT", 25, 15)
            bee.oled.write(card_id[:12], 5, 30)

            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # Đỏ
            bee.buzzer.play_song("A4:0.2 A4:0.2")

            return False

    def add_student(self):
        """Thêm học sinh mới"""
        bee.oled.clear()
        bee.oled.write("ADD STUDENT", 15, 0)
        bee.oled.write("Scan card...", 10, 20)

        timeout = 15
        start_time = time.time()

        while (time.time() - start_time) < timeout:
            card_id = self.rfid.scan_card()
            if card_id:
                if card_id not in self.students:
                    # Thẻ mới - thêm vào danh sách
                    student_name = f"Student_{len(self.students)+1}"
                    student_class = "10A1"

                    self.students[card_id] = {
                        "name": student_name,
                        "class": student_class
                    }
                    self.save_students()

                    bee.oled.clear()
                    bee.oled.write("STUDENT", 25, 0)
                    bee.oled.write("ADDED", 30, 15)
                    bee.oled.write(student_name, 5, 30)

                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
                    bee.buzzer.play_song("C4 E4 G4 C5")

                    time.sleep(2)
                    return
                else:
                    bee.oled.clear()
                    bee.oled.write("STUDENT", 25, 0)
                    bee.oled.write("EXISTS", 30, 15)
                    bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)
                    time.sleep(1)

            time.sleep(0.1)

        # Timeout
        bee.oled.clear()
        bee.oled.write("TIMEOUT", 25, 15)
        time.sleep(1)

    def show_statistics(self):
        """Hiển thị thống kê điểm danh"""
        present_count = len(self.attendance_log)
        total_count = len(self.students)
        absent_count = total_count - present_count

        bee.oled.clear()
        bee.oled.write("STATISTICS", 15, 0)
        bee.oled.write(f"Present: {present_count}", 10, 15)
        bee.oled.write(f"Absent: {absent_count}", 15, 30)
        bee.oled.write(f"Total: {total_count}", 20, 45)

        # Hiển thị trong 5 giây
        for i in range(50):
            if bee.is_button_pressed(bee.BUTTON_A) or bee.is_button_pressed(bee.BUTTON_B):
                break
            time.sleep(0.1)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        last_card_time = 0
        card_cooldown = 1

        while True:
            try:
                if not self.session_active:
                    # Chế độ chờ
                    bee.oled.clear()
                    bee.oled.write("Press A: Start", 5, 10)
                    bee.oled.write("Press B: Add", 15, 25)
                    bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)

                    if bee.is_button_pressed(bee.BUTTON_A):
                        self.start_session()
                    elif bee.is_button_pressed(bee.BUTTON_B):
                        self.add_student()

                else:
                    # Phiên điểm danh đang hoạt động
                    current_time = time.time()

                    # Đọc thẻ
                    if (current_time - last_card_time) > card_cooldown:
                        card_id = self.rfid.scan_card()
                        if card_id:
                            last_card_time = current_time
                            self.check_attendance(card_id)
                            time.sleep(2)  # Hiển thị kết quả 2 giây

                    # Kiểm tra nút điều khiển
                    if bee.is_button_pressed(bee.BUTTON_A):
                        self.show_statistics()
                    elif bee.is_button_pressed(bee.BUTTON_B):
                        self.end_session()

                time.sleep(0.1)

            except KeyboardInterrupt:
                if self.session_active:
                    self.end_session()
                break

# Chạy hệ thống điểm danh
if __name__ == "__main__":
    system = AttendanceSystem()
    system.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Access control**: Hệ thống kiểm soát truy cập đơn giản
2. **Card management**: Thêm/xóa thẻ từ danh sách được phép
3. **Visual feedback**: Hiển thị trạng thái trên OLED và LED
4. **Cooldown mechanism**: Tránh đọc thẻ liên tục

### Ví dụ nâng cao:

1. **Student database**: Quản lý thông tin học sinh với JSON
2. **Attendance logging**: Ghi log điểm danh với timestamp
3. **Session management**: Quản lý phiên điểm danh
4. **Statistics**: Thống kê và báo cáo điểm danh

## Bài tập mở rộng

1. **Hệ thống thanh toán**: Tạo hệ thống thanh toán với thẻ RFID và quản lý số dư
2. **Smart locker**: Tủ khóa thông minh mở bằng thẻ RFID cá nhân
3. **Pet feeder**: Máy cho ăn thú cưng tự động nhận diện bằng thẻ RFID

## Lỗi thường gặp

```{admonition} Lỗi: Không đọc được thẻ
:class: warning

**Nguyên nhân**: Khoảng cách quá xa hoặc thẻ không tương thích

**Giải pháp**:
- Đưa thẻ gần hơn (< 5cm)
- Kiểm tra thẻ có phải MIFARE Classic/NTAG
- Đảm bảo thẻ không bị hỏng
- Kiểm tra antenna không bị che khuất
```

```{admonition} Lỗi: Địa chỉ I2C không đúng
:class: warning

**Nguyên nhân**: Module có địa chỉ I2C khác mặc định

**Giải pháp**:
- Kiểm tra ASW switch trên module
- Thử các địa chỉ 0x2C, 0x2D, 0x2E, 0x2F
- Sử dụng I2C scanner để tìm địa chỉ
- Đọc datasheet module để xác định địa chỉ
```

```{admonition} Lỗi: File JSON bị lỗi
:class: warning

**Nguyên nhân**: File danh sách thẻ bị corrupt hoặc format sai

**Giải pháp**:
- Xóa file JSON và tạo lại
- Kiểm tra format JSON hợp lệ
- Backup danh sách thẻ thường xuyên
- Sử dụng try-except khi đọc file
```

## Tài nguyên tham khảo

-   [RC522 Datasheet](https://www.nxp.com/docs/en/data-sheet/MFRC522.pdf)
-   [MIFARE Classic Documentation](https://www.nxp.com/products/rfid-nfc/mifare-hf/mifare-classic:MC_41863)
-   [NTAG213/215/216 Datasheet](https://www.nxp.com/docs/en/data-sheet/NTAG213_215_216.pdf)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
