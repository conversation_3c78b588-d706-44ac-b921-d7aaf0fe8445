# BeeBrain - <PERSON><PERSON><PERSON>hiển Chính

## Giới thiệu

BeeBrain là module điều khiển trung tâm của BeE Board, tích hợp tất cả các thành phần phần cứng và cung cấp giao diện lập trình thống nhất. <PERSON><PERSON>le này quản lý các nút bấm, LED RGB, màn hình OLED, buzzer, động cơ, servo, cảm biến IMU và kết nối mạng.

BeeBrain hoạt động như "bộ não" của robot, điều phối tất cả các hoạt động và cung cấp API đơn giản để điều khiển robot một cách trực quan. Đ<PERSON><PERSON> là điểm khởi đầu cho mọi dự án với BeE Board.

**Ứng dụng thực tế:**

-   Robot di chuyển tự động
-   <PERSON><PERSON> thống IoT thông minh
-   Điều khiển từ xa qua WiFi/Bluetooth
-   Giám sát môi trường
-   <PERSON><PERSON><PERSON> chơi tương tác
-   Hệ thống giáo dục STEM

## Thông số kỹ thuật

| Thông số          | Giá trị                       |
| ----------------- | ----------------------------- |
| Vi điều khiển     | ESP32-S3                      |
| Tần số CPU        | 240MHz                        |
| RAM               | 512KB                         |
| Flash             | 8MB                           |
| WiFi              | 802.11 b/g/n                  |
| Bluetooth         | BLE 5.0                       |
| GPIO Ports        | 6 ports (PORT1-PORT6)         |
| Nút bấm tích hợp  | 2 (Button A, Button B)        |
| LED RGB tích hợp  | 2 LEDs (LED1, LED2)           |
| OLED tích hợp     | 128x64 pixels (SSD1306)       |
| Buzzer tích hợp   | PWM buzzer                    |
| IMU tích hợp      | MPU6050 (6-axis)              |
| Động cơ DC        | 2 channels                    |
| Servo motors      | 4 channels                    |
| Điện áp hoạt động | 6-8.4V (Li-ion) hoặc 5V (USB) |

## Giao diện lập trình

### Khởi tạo

BeeBrain cung cấp một instance sẵn có để sử dụng, hoặc có thể tạo instance mới nếu cần.

```python
# Sử dụng instance có sẵn
from BeeBrain import bee
bee.led1.on()

# Hoặc tạo instance mới
from BeeBrain import BeeBrain
my_robot = BeeBrain()
my_robot.led1.on()
```

### Điều khiển chuyển động

BeE board được tích hợp điều khiển 2 động cơ DC cho di chuyển và quay. BeeBrain cung cấp các hàm để điều khiển robot di chuyển một cách đơn giản.

`power`: Công suất di chuyển, từ 0% đến 100%.

`time`: Thời gian di chuyển (giây).

Nếu `time` = 0, robot sẽ di chuyển liên tục cho đến khi gọi `bee.stop_robot()`.

#### `move_forward(power, time)` - Di chuyển tiến

```python
# Di chuyển tiến với công suất 50% trong 2 giây
bee.move_forward(50, 2)

# Di chuyển tiến liên tục
bee.move_forward(30)
```

#### `move_backward(power, time)` - Di chuyển lùi

```python
# Di chuyển lùi với công suất 40% trong 1 giây
bee.move_backward(40, 1)
```

#### `turn_left(power, time)` / `turn_right(power, time)` - Rẽ trái/phải

```python
# Rẽ trái với công suất 30% trong 1 giây
bee.turn_left(30, 1)

# Rẽ phải liên tục
bee.turn_right(25)
```

#### `stop_robot()` - Dừng robot

2 động cơ được phanh cứng để dừng robot.

```python
bee.stop_robot()
```

### Điều khiển LED RGB tích hợp

BeE board tích hợp 2 LED RGB (NeoPixel) có thể hiển thị 16.7 triệu màu sắc.

#### `led1` / `led2` - Điều khiển LED

```python
# Bật LED với màu xanh lá
bee.led1.on()
bee.led1.set_color(0, 255, 0)  # RGB: đỏ, xanh lá, xanh dương

# Tắt LED
bee.led1.off()

# Điều khiển LED 2
bee.led2.set_color(255, 0, 0)  # Màu đỏ
bee.led2.on()
```

### Điều khiển OLED tích hợp

BeE board tích hợp màn hình OLED 128x64 pixels sử dụng chip SSD1306.

#### `oled` - Điều khiển màn hình OLED

```python
# Xóa màn hình
bee.oled.clear()

# Hiển thị văn bản
bee.oled.write("Hello BeE!", 10, 20)  # text, x, y

# Hiển thị nhiều dòng
bee.oled.clear()
bee.oled.write("Line 1", 0, 0)
bee.oled.write("Line 2", 0, 15)
bee.oled.write("Line 3", 0, 30)
bee.oled.show()
```

### Điều khiển Buzzer tích hợp

BeE board tích hợp buzzer PWM có thể phát âm thanh và nhạc.

#### `buzzer` - Điều khiển âm thanh

```python
# Phát một nốt nhạc
bee.buzzer.play_note("C4", 0.5)  # Nốt C4 trong 0.5 giây

# Phát một bài hát
bee.buzzer.play_song("C4 E4 G4 C5")

# Phát âm thanh với tần số tùy chỉnh
bee.buzzer.play_tone(440, 1000)  # 440Hz trong 1 giây
```

### Điều khiển IMU tích hợp

BeE board tích hợp cảm biến IMU MPU6050 6-axis (gia tốc + con quay hồi chuyển).

#### `imu` - Đọc dữ liệu cảm biến

```python
# Kiểm tra IMU có sẵn không
if bee.imu:
    # Cập nhật dữ liệu
    bee.imu.update()

    # Đọc góc nghiêng
    pitch = bee.imu.pitchDeg
    roll = bee.imu.rollDeg
    yaw = bee.imu.yawDeg

    # Đọc gia tốc
    accel = bee.imu.accel
    ax, ay, az = accel.x, accel.y, accel.z

    # Kiểm tra rung lắc
    if bee.imu.is_shaking():
        print("Robot đang rung!")
```

### Điều khiển nút bấm

BeE board tích hợp 2 nút bấm A (trái) và B (phải). BeeBrain cung cấp các hàm để kiểm tra trạng thái của nút.

| Biến              | Giá trị | Mô tả          |
| ----------------- | ------- | -------------- |
| `bee.BUTTON_A`    | 1       | Nút A (trái)   |
| `bee.BUTTON_B`    | 2       | Nút B (phải)   |
| `bee.BOTH_BUTTON` | 0       | Bất kỳ nút nào |

| Thuộc tính nút | Mô tả           |
| -------------- | --------------- |
| `bee.buttonA`  | Đối tượng nút A |
| `bee.buttonB`  | Đối tượng nút B |

| Hàm                              | Mô tả                              |
| -------------------------------- | ---------------------------------- |
| `is_button_pressed(button)`      | Kiểm tra nút được nhấn             |
| `is_button_released(button)`     | Kiểm tra nút được thả              |
| `is_button_long_pressed(button)` | Kiểm tra nút được nhấn giữ (>= 1s) |

#### `is_button_pressed(button)` - Kiểm tra nút được nhấn

```python
# Kiểm tra Button A
if bee.buttonA.is_pressed():
    print("Button A pressed")

# Kiểm tra Button B
if bee.buttonB.is_pressed():
    print("Button B pressed")

# Kiểm tra bất kỳ nút nào
if bee.is_button_pressed(bee.BOTH_BUTTON):
    print("Some button pressed")
```

### Điều khiển Motor và Servo onboard

BeE board tích hợp điều khiển 2 motor DC và 4 servo motor trực tiếp.

#### Motor DC onboard

```python
# Điều khiển motor 1
bee.motor1.run(50)    # Chạy tiến với tốc độ 50%
bee.motor1.run(-30)   # Chạy lùi với tốc độ 30%
bee.motor1.brake()    # Phanh motor

# Điều khiển motor 2
bee.motor2.run(40)    # Chạy tiến với tốc độ 40%
bee.motor2.brake()    # Phanh motor
```

#### Servo motor onboard

```python
# Điều khiển servo 1-4
bee.servo1.position(90)   # Xoay servo 1 đến góc 90°
bee.servo2.position(0)    # Xoay servo 2 đến góc 0°
bee.servo3.position(180)  # Xoay servo 3 đến góc 180°
bee.servo4.position(45)   # Xoay servo 4 đến góc 45°

# Thả servo (không giữ vị trí)
bee.servo1.release()
```

### Kết nối mạng

BeeBrain hỗ trợ kết nối WiFi, Bluetooth và MQTT.

#### `connect_wifi(ssid, password)` - Kết nối WiFi

`ssid`: Tên mạng WiFi.
`password`: Mật khẩu WiFi.

```python
sta_if = bee.connect_wifi("MyWiFi", "password123")
print("IP:", sta_if.ifconfig()[0])
```

#### `connect_mqtt(user, password)` - Kết nối MQTT

`user`: Tên đăng nhập MQTT.
`password`: Mật khẩu MQTT.

```python
mqtt = bee.connect_mqtt("user", "pass")
mqtt.connect()
```

#### `connect_ble(name)` - Kết nối Bluetooth

`name`: Thiết lập tên thiết bị Bluetooth.

```python
ble = bee.connect_ble("MyRobot")
ble.advertise()
```

### GPIO và Analog

#### `digital_write(port, value)` / `digital_read(port)` - GPIO

BeE board có 6 port GPIO, mỗi port có 2 chân. BeeBrain cung cấp các hàm để đọc và ghi digital cả 6 port.

`port`: PORT1-PORT6.
`value`: Giá trị để ghi (0 hoặc 1).

```python
# Ghi digital
bee.digital_write(bee.PORT1, 1)

# Đọc digital
value = bee.digital_read(bee.PORT1)
```

#### `analog_read(port, mode)` - Đọc analog

BeE board có 6 port analog, mỗi port có 2 chân. BeeBrain cung cấp hàm để đọc analog từ cả 6 port.

`port`: PORT1-PORT6.
`mode`: Chế độ đọc (bee.VALUE hoặc bee.PERCENT).

```python
# Đọc giá trị thô (0-4095)
raw_value = bee.analog_read(bee.PORT2, bee.VALUE)

# Đọc phần trăm (0-100%)
percent = bee.analog_read(bee.PORT2, bee.PERCENT)
```

## Ví dụ Blockly

```blockly
when program starts:
    show "Hello BeE!" on OLED
    set LED 1 to GREEN
    play startup sound "C4 E4 G4"
    calibrate IMU

when button A pressed:
    show "Moving Forward" on OLED
    set LED 1 to BLUE
    move forward at 50% for 2 seconds
    turn right at 30% for 1 second
    stop robot

when button B pressed:
    show "Servo Demo" on OLED
    set LED 2 to YELLOW
    move servo 1 to 90 degrees
    move servo 2 to 0 degrees
    play sound "A4 C5"

when IMU shaking detected:
    show "Shaking!" on OLED
    set LED 1 to RED
    play alarm sound
    stop robot

forever:
    read IMU data
    show pitch and roll on OLED
    if WiFi connected:
        send telemetry data
```

## Ví dụ Python

### Ví dụ cơ bản - Robot điều khiển bằng nút

```python
from BeeBrain import bee
import time

def setup():
    """Khởi tạo robot"""
    bee.oled.clear()
    bee.oled.write("Robot Ready!", 10, 20)
    bee.led1.set_color(0, 255, 0)  # LED xanh
    bee.led1.on()
    bee.buzzer.play_song("C4 E4 G4")

    # Khởi tạo IMU nếu có
    if bee.imu:
        bee.oled.write("IMU: OK", 10, 35)
    else:
        bee.oled.write("IMU: Not found", 10, 35)

def button_control():
    """Điều khiển robot bằng nút bấm"""
    while True:
        if bee.buttonA.is_pressed():
            # Button A: Di chuyển tiến và hiển thị IMU
            bee.oled.clear()
            bee.oled.write("Moving Forward", 5, 10)
            bee.led1.set_color(0, 0, 255)  # LED xanh dương
            bee.move_forward(40, 1)

            # Hiển thị thông tin IMU nếu có
            if bee.imu:
                bee.imu.update()
                bee.oled.write(f"Pitch: {bee.imu.pitchDeg:.1f}", 0, 25)
                bee.oled.write(f"Roll: {bee.imu.rollDeg:.1f}", 0, 40)

        elif bee.buttonB.is_pressed():
            # Button B: Demo servo và buzzer
            bee.oled.clear()
            bee.oled.write("Servo Demo", 25, 20)
            bee.led2.set_color(255, 255, 0)  # LED vàng
            bee.led2.on()

            # Demo servo
            bee.servo1.position(90)
            bee.servo2.position(0)
            bee.buzzer.play_song("A4 C5 E5")
            time.sleep(1)
            bee.servo1.position(0)
            bee.servo2.position(90)

        else:
            # Không có nút nào được nhấn
            bee.oled.clear()
            bee.oled.write("Press A or B", 10, 15)
            bee.oled.write("A: Move + IMU", 5, 30)
            bee.oled.write("B: Servo Demo", 5, 45)
            bee.led1.set_color(0, 255, 0)  # LED xanh
            bee.led2.off()
            bee.stop_robot()

        time.sleep(0.1)

# Chạy chương trình
setup()
button_control()
```

### Ví dụ nâng cao - Robot IoT thông minh

```python
from BeeBrain import bee
import time
import json

class SmartRobot:
    def __init__(self):
        self.wifi_connected = False
        self.mqtt_client = None
        self.auto_mode = False
        self.sensor_data = {}

    def setup_connections(self):
        """Thiết lập kết nối WiFi và MQTT"""
        try:
            # Kết nối WiFi
            bee.oled.clear()
            bee.oled.write("Connecting WiFi...", 0, 10)

            sta_if = bee.connect_wifi("YourWiFi", "YourPassword")
            self.wifi_connected = True

            bee.oled.clear()
            bee.oled.write("WiFi Connected!", 5, 10)
            bee.oled.write(sta_if.ifconfig()[0], 0, 25)

            # Kết nối MQTT
            self.mqtt_client = bee.connect_mqtt("robot_user", "robot_pass")
            self.mqtt_client.set_callback(self.mqtt_callback)
            self.mqtt_client.connect()
            self.mqtt_client.subscribe("robot/control")

            bee.buzzer.play_song("C4 E4 G4 C5")
            bee.led1.set_color(0, 255, 0)  # Xanh = kết nối thành công
            bee.led1.on()

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Connection Failed", 0, 10)
            bee.led1.set_color(255, 0, 0)  # Đỏ = lỗi
            bee.led1.on()
            bee.buzzer.play_song("A4:0.2 SIL:0.1 A4:0.2")

    def mqtt_callback(self, topic, msg):
        """Xử lý tin nhắn MQTT"""
        try:
            command = json.loads(msg.decode())

            if command["action"] == "move":
                direction = command["direction"]
                power = command.get("power", 30)
                duration = command.get("duration", 1)

                if direction == "forward":
                    bee.move_forward(power, duration)
                elif direction == "backward":
                    bee.move_backward(power, duration)
                elif direction == "left":
                    bee.turn_left(power, duration)
                elif direction == "right":
                    bee.turn_right(power, duration)
                elif direction == "stop":
                    bee.stop_robot()

            elif command["action"] == "led":
                r = command.get("r", 0)
                g = command.get("g", 0)
                b = command.get("b", 0)
                led_id = command.get("led", 1)
                if led_id == 1:
                    bee.led1.set_color(r, g, b)
                    bee.led1.on()
                elif led_id == 2:
                    bee.led2.set_color(r, g, b)
                    bee.led2.on()

            elif command["action"] == "sound":
                melody = command.get("melody", "C4 E4 G4")
                bee.buzzer.play_song(melody)

            elif command["action"] == "auto":
                self.auto_mode = command.get("enable", False)

        except Exception as e:
            print("MQTT callback error:", e)

    def read_sensors(self):
        """Đọc dữ liệu cảm biến"""
        self.sensor_data = {
            "button_a": bee.buttonA.is_pressed(),
            "button_b": bee.buttonB.is_pressed(),
            "timestamp": time.time()
        }

        # Đọc IMU nếu có
        if bee.imu:
            try:
                bee.imu.update()
                self.sensor_data.update({
                    "pitch": bee.imu.pitchDeg,
                    "roll": bee.imu.rollDeg,
                    "yaw": bee.imu.yawDeg,
                    "shaking": bee.imu.is_shaking()
                })
            except:
                pass

        # Đọc analog từ các port
        for i, port in enumerate([bee.PORT2, bee.PORT3, bee.PORT5]):
            try:
                value = bee.analog_read(port, bee.PERCENT)
                self.sensor_data[f"analog_{i}"] = value
            except:
                pass

    def send_telemetry(self):
        """Gửi dữ liệu telemetry qua MQTT"""
        if self.mqtt_client and self.wifi_connected:
            try:
                payload = json.dumps(self.sensor_data)
                self.mqtt_client.publish("robot/telemetry", payload)
            except:
                pass

    def auto_behavior(self):
        """Hành vi tự động của robot"""
        if not self.auto_mode:
            return

        # Kiểm tra nút bấm
        if self.sensor_data.get("button_a"):
            bee.move_forward(30, 0.5)
            bee.led1.set_color(0, 0, 255)
            bee.led1.on()

        elif self.sensor_data.get("button_b"):
            bee.turn_right(25, 0.5)
            bee.led1.set_color(255, 255, 0)
            bee.led1.on()

        # Kiểm tra rung lắc
        if self.sensor_data.get("shaking"):
            bee.buzzer.play_song("A5:0.1 SIL:0.05 A5:0.1")
            bee.led1.set_color(255, 0, 0)
            bee.led1.on()
            bee.stop_robot()

        # Hiển thị thông tin trên OLED
        bee.oled.clear()
        bee.oled.write("Auto Mode ON", 10, 0)
        bee.oled.write(f"Pitch: {self.sensor_data.get('pitch', 0):.1f}", 0, 15)
        bee.oled.write(f"Roll: {self.sensor_data.get('roll', 0):.1f}", 0, 30)
        bee.oled.write(f"Yaw: {self.sensor_data.get('yaw', 0):.1f}", 0, 45)

    def run(self):
        """Vòng lặp chính"""
        self.setup_connections()

        last_telemetry = 0

        while True:
            try:
                # Kiểm tra tin nhắn MQTT
                if self.mqtt_client:
                    self.mqtt_client.check_msg()

                # Đọc cảm biến
                self.read_sensors()

                # Hành vi tự động
                self.auto_behavior()

                # Gửi telemetry mỗi 5 giây
                current_time = time.time()
                if current_time - last_telemetry > 5:
                    self.send_telemetry()
                    last_telemetry = current_time

                time.sleep(0.1)

            except KeyboardInterrupt:
                print("Stopping robot...")
                bee.stop_robot()
                bee.led1.off()
                bee.led2.off()
                break
            except Exception as e:
                print("Error:", e)
                time.sleep(1)

# Chạy robot thông minh
if __name__ == "__main__":
    robot = SmartRobot()
    robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Setup function**: Khởi tạo OLED, LED, buzzer và IMU
2. **Button control**: Sử dụng `buttonA.is_pressed()` và `buttonB.is_pressed()`
3. **Onboard components**: Demo LED RGB, servo, buzzer và IMU tích hợp
4. **Visual feedback**: Hiển thị thông tin IMU và trạng thái trên OLED
5. **Simple logic**: Logic đơn giản dễ hiểu cho người mới bắt đầu

### Ví dụ nâng cao:

1. **IoT integration**: Kết nối WiFi và MQTT cho điều khiển từ xa
2. **Sensor fusion**: Kết hợp nhiều cảm biến (IMU, analog, buttons onboard)
3. **Auto behavior**: Robot có thể hoạt động tự động dựa trên cảm biến
4. **Onboard components**: Sử dụng đầy đủ OLED, LED, buzzer, IMU tích hợp
5. **Error handling**: Xử lý lỗi và khôi phục kết nối

## Bài tập mở rộng

1. **Robot tuần tra**: Tạo robot tự động tuần tra và báo cáo qua MQTT khi phát hiện chuyển động
2. **Trạm thời tiết IoT**: Kết hợp cảm biến môi trường và gửi dữ liệu lên cloud
3. **Robot điều khiển giọng nói**: Tích hợp với AI assistant để điều khiển bằng giọng nói

## Lỗi thường gặp

```{admonition} Lỗi: WiFi không kết nối được
:class: warning

**Nguyên nhân**: Sai thông tin WiFi hoặc tín hiệu yếu

**Giải pháp**:
- Kiểm tra tên WiFi và mật khẩu
- Đảm bảo robot ở gần router
- Thử reset WiFi và kết nối lại
- Kiểm tra băng tần (2.4GHz vs 5GHz)
```

```{admonition} Lỗi: Robot không di chuyển
:class: warning

**Nguyên nhân**: Pin yếu hoặc motor không được kết nối

**Giải pháp**:
- Kiểm tra mức pin (>3.5V)
- Đảm bảo motor được kết nối đúng
- Thử giảm công suất motor
- Kiểm tra driver motor TB6612
```

```{admonition} Lỗi: OLED không hiển thị
:class: warning

**Nguyên nhân**: Kết nối I2C lỏng hoặc địa chỉ sai

**Giải pháp**:
- Kiểm tra kết nối PORT0 (SDA, SCL)
- Thử reset board
- Kiểm tra địa chỉ I2C (0x3C)
- Đảm bảo nguồn 3.3V ổn định
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [ESP32-S3 Technical Reference](https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/)
-   [MicroPython ESP32 Guide](https://docs.micropython.org/en/latest/esp32/quickref.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
