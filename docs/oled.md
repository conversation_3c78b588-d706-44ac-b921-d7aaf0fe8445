# Oled - <PERSON><PERSON>le M<PERSON>nh OLED

## Giới thiệu

Module Oled cung cấp giao diện điều khiển màn hình OLED SSD1306 cho BeE Board. Màn hình OLED 128x64 pixel monochrome cho phép hiển thị văn bản, hình ảnh và đồ họa cơ bản với độ tương phản cao và tiêu thụ điện năng thấp.

OLED (Organic Light-Emitting Diode) là công nghệ hiển thị tự phát sáng, không cần đèn nền, cho màu đen sâu và độ tương phản cao. Màn hình có thể xoay và hỗ trợ nhiều chế độ hiển thị khác nhau.

**Ứng dụng thực tế:**

-   Hiển thị thông tin cảm biến
-   Tạo menu điều hướng
-   Hi<PERSON>n thị đồng hồ và lịch
-   Vẽ đồ thị dữ liệu
-   Tạo game đơn giản
-   Hiển thị trạng thái hệ thống

## Thông số kỹ thuật

| Thông số           | Giá trị         |
| ------------------ | --------------- |
| Độ phân giải       | 128x64 pixels   |
| Loại màn hình      | OLED Monochrome |
| Driver IC          | SSD1306         |
| Giao tiếp          | I2C (SCL, SDA)  |
| Địa chỉ I2C        | 0x3C            |
| Điện áp hoạt động  | 3.3V - 5V       |
| Dòng tiêu thụ      | ~20mA (typical) |
| Góc nhìn           | >160°           |
| Nhiệt độ hoạt động | -40°C đến +85°C |

## Giao diện lập trình

### Khởi tạo

```python
from BeeBrain import bee
from Oled import Oled

# Khởi tạo OLED onboard
oled = bee.oled

# Khởi tạo với xoay màn hình 180 độ
oled_rotated = Oled(port=bee.PORT1, rotation=180)
```

### Các phương thức chính

#### `write(text, x, y, color)` - Viết văn bản

```python
# Viết text tại vị trí (0,0)
oled.write("Hello BeE!", 0, 0)

# Viết với tọa độ tùy chỉnh
oled.write("Temperature: 25°C", 10, 20)
```

#### `long_text(text)` - Hiển thị văn bản dài

```python
# Tự động xuống dòng
oled.long_text("Đây là một đoạn văn bản rất dài sẽ được tự động xuống dòng")
```

#### `clear()` - Xóa màn hình

```python
oled.clear()
oled.show()  # Cập nhật hiển thị
```

#### `draw_pixel(x, y, color)` - Vẽ điểm

```python
oled.draw_pixel(64, 32, 1)  # Vẽ điểm ở giữa màn hình
```

#### `draw_line(x1, y1, x2, y2, color)` - Vẽ đường thẳng

```python
oled.draw_line(0, 0, 127, 63, 1)  # Vẽ đường chéo
```

#### `draw_rect(x, y, w, h, color)` - Vẽ hình chữ nhật

```python
oled.draw_rect(10, 10, 50, 30, 1)  # Vẽ hình chữ nhật
```

#### `draw_circle(x, y, r, color)` - Vẽ hình tròn

```python
oled.draw_circle(64, 32, 20, 1)  # Vẽ hình tròn ở giữa
```

## Ví dụ Blockly

```blockly
when program starts:
    clear OLED display
    write "BeE Board" at (20, 10)
    write "Temperature:" at (0, 30)
    write "25°C" at (80, 30)
    draw rectangle at (0, 50) size (128, 10)
    show display
```

## Ví dụ Python

### Ví dụ cơ bản - Hiển thị thông tin

```python
from BeeBrain import bee
import time

# Khởi tạo OLED
oled = bee.oled

# Hiển thị thông tin cơ bản
oled.clear()
oled.write("BeE Board v1.0", 15, 0)
oled.write("Status: Ready", 10, 15)
oled.write("Time: 12:34:56", 10, 30)

# Vẽ khung viền
oled.draw_rect(0, 0, 128, 64, 1)

# Hiển thị
oled.show()

# Giữ hiển thị 5 giây
time.sleep(5)
```

### Ví dụ nâng cao - Đồng hồ kỹ thuật số

```python
from BeeBrain import bee
from Oled import Oled
import time
import machine

# Khởi tạo OLED
oled = Oled(port=bee.PORT1)

def draw_clock():
    """Vẽ giao diện đồng hồ"""
    oled.clear()

    # Lấy thời gian hiện tại (giả lập)
    current_time = time.localtime()
    hour = current_time[3]
    minute = current_time[4]
    second = current_time[5]

    # Hiển thị thời gian lớn
    time_str = f"{hour:02d}:{minute:02d}:{second:02d}"
    oled.write(time_str, 25, 20, 1)

    # Hiển thị ngày
    day = current_time[2]
    month = current_time[1]
    year = current_time[0]
    date_str = f"{day:02d}/{month:02d}/{year}"
    oled.write(date_str, 30, 35, 1)

    # Vẽ khung
    oled.draw_rect(5, 5, 118, 54, 1)

    # Vẽ dấu chấm nhấp nháy giữa giờ:phút
    if second % 2 == 0:
        oled.draw_pixel(63, 25, 1)
        oled.draw_pixel(63, 27, 1)

    oled.show()

def draw_analog_clock():
    """Vẽ đồng hồ kim"""
    oled.clear()

    # Tâm đồng hồ
    center_x, center_y = 64, 32
    radius = 25

    # Vẽ mặt đồng hồ
    oled.draw_circle(center_x, center_y, radius, 1)

    # Vẽ các vạch giờ
    for i in range(12):
        angle = i * 30 * 3.14159 / 180  # Chuyển sang radian
        x1 = center_x + int((radius - 5) * math.sin(angle))
        y1 = center_y - int((radius - 5) * math.cos(angle))
        x2 = center_x + int(radius * math.sin(angle))
        y2 = center_y - int(radius * math.cos(angle))
        oled.draw_line(x1, y1, x2, y2, 1)

    # Lấy thời gian
    current_time = time.localtime()
    hour = current_time[3] % 12
    minute = current_time[4]

    # Vẽ kim giờ
    hour_angle = (hour * 30 + minute * 0.5) * 3.14159 / 180
    hour_x = center_x + int(15 * math.sin(hour_angle))
    hour_y = center_y - int(15 * math.cos(hour_angle))
    oled.draw_line(center_x, center_y, hour_x, hour_y, 1)

    # Vẽ kim phút
    minute_angle = minute * 6 * 3.14159 / 180
    minute_x = center_x + int(20 * math.sin(minute_angle))
    minute_y = center_y - int(20 * math.cos(minute_angle))
    oled.draw_line(center_x, center_y, minute_x, minute_y, 1)

    # Vẽ tâm
    oled.draw_circle(center_x, center_y, 2, 1)

    oled.show()

# Chạy đồng hồ
import math

while True:
    draw_clock()
    time.sleep(1)

    # Chuyển sang đồng hồ kim sau 10 giây
    if time.time() % 20 < 10:
        draw_analog_clock()
        time.sleep(1)
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Khởi tạo**: Tạo đối tượng Oled với cổng kết nối
2. **Xóa màn hình**: Sử dụng `clear()` để xóa buffer
3. **Viết văn bản**: Sử dụng `write()` với tọa độ x, y
4. **Vẽ hình**: Sử dụng các hàm vẽ cơ bản
5. **Cập nhật**: Gọi `show()` để hiển thị lên màn hình

### Ví dụ nâng cao:

1. **Lấy thời gian**: Sử dụng `time.localtime()` để lấy thời gian hệ thống
2. **Định dạng**: Format thời gian thành chuỗi hiển thị
3. **Đồng hồ kim**: Tính toán góc và vẽ kim dựa trên thời gian
4. **Hiệu ứng**: Tạo dấu chấm nhấp nháy và chuyển đổi giao diện

## Bài tập mở rộng

1. **Màn hình cảm biến**: Hiển thị dữ liệu từ nhiều cảm biến (nhiệt độ, độ ẩm, ánh sáng) với biểu đồ
2. **Game Snake**: Tạo trò chơi rắn săn mồi đơn giản trên OLED
3. **Menu cài đặt**: Tạo hệ thống menu phân cấp để cấu hình các thông số

## Lỗi thường gặp

```{admonition} Lỗi: Màn hình không hiển thị
:class: warning

**Nguyên nhân**: Kết nối I2C sai hoặc địa chỉ không đúng

**Giải pháp**:
- Kiểm tra kết nối SCL, SDA
- Đảm bảo nguồn 3.3V/5V ổn định
- Kiểm tra địa chỉ I2C (thường là 0x3C)
- Thử với pull-up resistor 4.7kΩ
```

```{admonition} Lỗi: Văn bản bị cắt
:class: warning

**Nguyên nhân**: Tọa độ vượt quá kích thước màn hình

**Giải pháp**:
- Đảm bảo x < 128, y < 64
- Tính toán độ dài text trước khi hiển thị
- Sử dụng `long_text()` cho văn bản dài
```

```{admonition} Lỗi: Hiển thị chậm hoặc giật
:class: warning

**Nguyên nhân**: Gọi `show()` quá nhiều lần hoặc I2C chậm

**Giải pháp**:
- Chỉ gọi `show()` khi cần cập nhật
- Tăng tốc độ I2C nếu có thể
- Tối ưu hóa code vẽ
- Sử dụng buffer để giảm số lần cập nhật
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beeblock.vn/docs/bee-ide)
-   [SSD1306 Datasheet](https://cdn-shop.adafruit.com/datasheets/SSD1306.pdf)
-   [MicroPython FrameBuffer](https://docs.micropython.org/en/latest/library/framebuf.html)
-   [BeE IDE](https://beeblock.vn/studio/bee-ide)
