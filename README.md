# BeE Board - MicroPython Framework

**Framework MicroPython cho BeE Board - Nền tảng robotics STEM giáo dục**

[![License: Proprietary](https://img.shields.io/badge/License-Proprietary-orange.svg)](https://beeblock.vn) [![Python Version](https://img.shields.io/badge/Python-3.6+-blue.svg)](https://python.org) [![MicroPython](https://img.shields.io/badge/MicroPython-1.26.0-green.svg)](https://micropython.org) [![ESP32](https://img.shields.io/badge/ESP32-S3-red.svg)](https://www.espressif.com/en/products/socs/esp32-s3)

---

**© 2025 BeE STEM Solutions. All rights reserved.**

_This document and its contents are proprietary to BeE STEM Solutions. No part of this publication may be reproduced, distributed, or transmitted in any form or by any means, including photocopying, recording, or other electronic or mechanical methods, without the prior written permission of the copyright owner._

---

## Giới thiệu tổng quan

BeE Board là một framework MicroPython mạnh mẽ được thiết kế đặc biệt cho giáo dục STEM và robotics. Framework cung cấp một bộ thư viện phong phú và dễ sử dụng để điều khiển các module phần cứng trên BeE Board, giúp học sinh và giáo viên dễ dàng tiếp cận với lập trình nhúng, IoT và robotics.

### Tính năng chính

-   **BeeBrain**: Module điều khiển trung tâm tích hợp WiFi, Bluetooth, IMU
-   **Điều khiển đa dạng**: Button, GPIO, Gamepad MQTT
-   **Hiển thị phong phú**: OLED, LED Matrix, LED 7-Segment, NeoPixel RGB
-   **Âm thanh**: Buzzer với khả năng phát nhạc và tạo hiệu ứng âm thanh
-   **Cảm biến thông minh**: Ultrasonic, Color, Line Detect, MPU6050 IMU
-   **Điều khiển động cơ**: DC Motor, Servo Motor với PCA9685
-   **Nhận diện**: RFID/NFC RC522
-   **Kết nối**: WiFi, Bluetooth LE, MQTT

### Ứng dụng

-   **Giáo dục STEM**: Dạy lập trình nhúng và robotics
-   **Robot thông minh**: Tránh vật cản, theo đường, điều khiển từ xa
-   **IoT Projects**: Nhà thông minh, trạm thời tiết, hệ thống giám sát
-   **Game và giải trí**: Console game, hiệu ứng ánh sáng, âm thanh tương tác
-   **Automation**: Hệ thống tự động hóa, kiểm soát truy cập

### Kiến trúc hệ thống

```
BeE Board Framework
├── BeeBrain (Core Controller)
├── Hardware Modules
│   ├── Input: Button, GPIO, Sensors
│   ├── Output: LED, OLED, Buzzer, Motors
│   ├── Communication: WiFi, BLE, MQTT
│   └── Storage: RFID, Internal Flash
├── Development Tools
│   ├── BeE IDE (Blockly)
│   └── MicroPython REPL
└── Documentation & Examples
```

---

## Các module được hỗ trợ

### Module điều khiển chính

-   **BeeBrain**: Controller trung tâm với WiFi, Bluetooth, IMU tích hợp
-   **MotorOnBoard**: Motor DC và servo tích hợp trên board

### Module điều khiển cơ bản

-   **Button**: Nút bấm với debounce và xử lý sự kiện
-   **GPIO**: Điều khiển GPIO đơn giản cho LED, relay
-   **Gamepad**: Điều khiển từ xa qua MQTT

### Module hiển thị

-   **Neopixel**: LED RGB WS2812B với 16.7 triệu màu
-   **Oled**: Màn hình OLED 128x64 với graphics
-   **LedMatrix**: Ma trận LED 5x5 với hiệu ứng
-   **LedSegment**: LED 7 đoạn 4 chữ số

### Module âm thanh

-   **Buzzer**: Buzzer với khả năng phát nhạc và tạo tone

### Module cảm biến

-   **Ultrasonic**: Cảm biến siêu âm đo khoảng cách
-   **ColorDetect**: Cảm biến màu TCS34725
-   **LineDetect**: Cảm biến dò đường 4 kênh
-   **Mpu6050**: IMU 6 trục (accelerometer + gyroscope)

### Module động cơ

-   **Motor**: Điều khiển DC motor và servo qua PCA9685

### Module nhận diện

-   **RC522**: Đọc thẻ RFID/NFC

---

## Cách tạo module mới

### 1. Cấu trúc module chuẩn

Tạo file module mới trong thư mục `deploy/` với cấu trúc sau:

```python
# deploy/NewModule.py

from micropython import const
from machine import Pin, I2C
import time

class NewModule:
    """
    @desc: Mô tả chức năng module
           Chi tiết về tính năng và cách sử dụng

    @example:
        >>> module = NewModule(port=PORT1)
        >>> module.do_something()
        >>> result = module.get_data()

    @version: 1.0.0
    """

    # Constants
    DEFAULT_ADDRESS = const(0x48)

    def __init__(self, port, param1=None, param2=100):
        """
        @desc: Khởi tạo module với các tham số cần thiết

        @args:
            - port: BeE board port object (PORT1-PORT6)
            - param1: Tham số tùy chọn
            - param2: Tham số với giá trị mặc định

        @raises:
            - ValueError: Nếu tham số không hợp lệ
            - RuntimeError: Nếu khởi tạo phần cứng thất bại

        @example:
            >>> module = NewModule(bee.PORT1, param2=200)
        """
        self.port = port
        self.param1 = param1
        self.param2 = param2

        # Khởi tạo phần cứng
        self._init_hardware()

    def _init_hardware(self):
        """Khởi tạo phần cứng (private method)"""
        try:
            # Ví dụ khởi tạo I2C
            self.i2c = I2C(
                scl=Pin(self.port.use_pins(1)),
                sda=Pin(self.port.use_pins(2))
            )

            # Kiểm tra kết nối
            if self.DEFAULT_ADDRESS not in self.i2c.scan():
                raise RuntimeError("Module not found")

        except Exception as e:
            raise RuntimeError(f"Hardware init failed: {e}")

    def do_something(self, value):
        """
        @desc: Thực hiện chức năng chính của module

        @args:
            - value: Giá trị đầu vào

        @returns:
            bool: True nếu thành công

        @example:
            >>> success = module.do_something(42)
        """
        # Implementation here
        return True

    def get_data(self):
        """
        @desc: Lấy dữ liệu từ module

        @returns:
            tuple: (data1, data2, data3)

        @example:
            >>> x, y, z = module.get_data()
        """
        # Implementation here
        return (0, 0, 0)
```

### 2. Quy ước đặt tên

-   **Class name**: `ModuleName` (PascalCase)
-   **File name**: `ModuleName.py`
-   **Method names**: `snake_case`
-   **Constants**: `UPPER_CASE`
-   **Private methods**: `_private_method`

### 3. Tích hợp vào BeeBrain

Thêm module vào `BeeBrain.py`:

```python
# Trong BeeBrain.py
from NewModule import NewModule

class BeeBrain:
    def __init__(self):
        # ... existing code ...

        # Khởi tạo module mới (nếu cần)
        try:
            self.new_module = NewModule(self.PORT1)
        except:
            self.new_module = None
            print("NewModule not found")
```

### 4. Testing module

Tạo file test đơn giản:

```python
# test_new_module.py
from BeeBrain import bee
from NewModule import NewModule

def test_new_module():
    try:
        # Test khởi tạo
        module = NewModule(bee.PORT1)
        print("✓ Module initialized")

        # Test các chức năng
        result = module.do_something(42)
        print(f"✓ do_something: {result}")

        data = module.get_data()
        print(f"✓ get_data: {data}")

        print("✓ All tests passed!")

    except Exception as e:
        print(f"✗ Test failed: {e}")

if __name__ == "__main__":
    test_new_module()
```

---

## Cách thêm documentation

### 1. Cấu trúc file docs

Tạo file documentation trong thư mục `docs/` với tên `module-name.md`:

````markdown
# NewModule - Module Mô Tả Ngắn

## Giới thiệu

Mô tả chi tiết về module, chức năng và ứng dụng thực tế.

**Ứng dụng thực tế:**

-   Ứng dụng 1
-   Ứng dụng 2
-   Ứng dụng 3

## Thông số kỹ thuật

| Thông số          | Giá trị      |
| ----------------- | ------------ |
| Điện áp hoạt động | 3.3V - 5V    |
| Giao tiếp         | I2C/SPI/UART |
| Tần số hoạt động  | 100kHz       |

## Giao diện lập trình

### Khởi tạo

```python
from NewModule import NewModule
module = NewModule(bee.PORT1)
```
````

### Các phương thức chính

-   `do_something(value)`: Mô tả chức năng
-   `get_data()`: Lấy dữ liệu

## Ví dụ Blockly

```blockly
when program starts:
    set module to NewModule port 1
    do something with value 42
```

## Ví dụ Python

### Ví dụ cơ bản

```python
# Code ví dụ đơn giản
```

### Ví dụ nâng cao

```python
# Code ví dụ phức tạp hơn
```

## Giải thích mã

Giải thích từng phần code...

## Bài tập mở rộng

1. Bài tập 1
2. Bài tập 2

## Lỗi thường gặp

```{admonition} Lỗi: Mô tả lỗi
:class: warning
**Nguyên nhân**: ...
**Giải pháp**: ...
```

## Tài nguyên tham khảo

-   Link 1
-   Link 2

````

### 2. Cập nhật docs/README.md

Thêm module mới vào danh sách trong `docs/README.md`:

```markdown
#### BeeNewModule - Module Mô Tả](bee-new-module.md)

Mô tả ngắn về chức năng module

- Tính năng 1
- Tính năng 2
- Ví dụ: Ứng dụng cụ thể
````

### 3. Quy ước viết docs

-   **Tiêu đề**: Sử dụng emoji và tên module rõ ràng
-   **Cấu trúc**: Theo template chuẩn (9 phần chính)
-   **Code examples**: Luôn có ví dụ cơ bản và nâng cao
-   **Giải thích**: Từng dòng code quan trọng được giải thích
-   **Troubleshooting**: Liệt kê lỗi thường gặp và cách khắc phục

---

## Deploy cho ESP32-S3

### 1. Yêu cầu hệ thống

#### Phần cứng

-   **BeE Board**: ESP32-S3 based development board
-   **RAM**: Tối thiểu 512KB SRAM, khuyến nghị có PSRAM
-   **Flash**: Tối thiểu 4MB, khuyến nghị 8MB+
-   **USB**: Cổng USB-C cho lập trình và debug
-   **Nguồn**: 5V/2A qua USB hoặc DC adapter

#### Phần mềm

-   **Python**: 3.6+ (cho development tools)
-   **ESP-IDF**: v5.4.2 (cho build firmware)
-   **MicroPython**: v1.26.0
-   **Tools**: esptool, ampy, rshell

### 2. Cài đặt môi trường phát triển

#### Cài đặt Python tools

```bash
# Cài đặt các công cụ cần thiết
pip install esptool ampy rshell

# Kiểm tra version
esptool.py version
ampy --version
rshell --version
```

#### Cài đặt ESP-IDF (cho build firmware)

```bash
# Clone ESP-IDF
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
git checkout v5.4.2
git submodule update --init --recursive

# Cài đặt
./install.sh
source export.sh
```

### 3. Build firmware tùy chỉnh

#### Clone MicroPython

```bash
git clone https://github.com/micropython/micropython.git
cd micropython
git checkout v1.26.0
git submodule update --init --recursive
```

#### Cấu hình board cho BeE Board

```bash
# Tạo board config tùy chỉnh
cat > ports/esp32/boards/BEE_BOARD_S3/mpconfigboard.h << 'EOF'
#ifndef MICROPY_HW_BOARD_NAME
#define MICROPY_HW_BOARD_NAME "BeeBrain"
#endif
#ifndef MICROPY_HW_MCU_NAME
#define MICROPY_HW_MCU_NAME "ESP32S3"
#endif
EOF

# Copy modules vào firmware
cp /path/to/beeblock_micropython/deploy/* ports/esp32/modules/
```

#### Build firmware

```bash
# Build mpy-cross
make -C mpy-cross

# Build firmware cho ESP32-S3
cd ports/esp32
make submodules

# Với PSRAM (khuyến nghị)
make BOARD=ESP32_GENERIC_S3_SPIRAM

# Không PSRAM
make BOARD=ESP32_GENERIC_S3
```

### 4. Flash firmware lên board

#### Xóa flash và flash firmware

```bash
# Xóa toàn bộ flash
esptool.py --chip esp32s3 --port /dev/ttyUSB0 erase_flash

# Flash firmware (thay đổi port phù hợp)
esptool.py --chip esp32s3 --port /dev/ttyUSB0 --baud 460800 write_flash -z 0x0 firmware.bin
```

#### Kiểm tra kết nối

```bash
# Kết nối REPL
rshell --port /dev/ttyUSB0 --baud 115200
> repl
>>> import sys
>>> sys.implementation
>>> from BeeBrain import bee
>>> bee.oled.text("Hello BeE!", 0, 0, 1)
>>> bee.oled.show()
```

### 5. Upload code lên board

#### Sử dụng ampy

```bash
# Upload file đơn lẻ
ampy --port /dev/ttyUSB0 put main.py

# Upload thư mục
ampy --port /dev/ttyUSB0 put deploy/ /

# Liệt kê files trên board
ampy --port /dev/ttyUSB0 ls
```

#### Sử dụng rshell

```bash
# Kết nối và upload
rshell --port /dev/ttyUSB0
> cp main.py /pyboard/
> cp -r deploy/ /pyboard/
> ls /pyboard
```

#### Sử dụng .micropythonrc config

Tạo file `.micropythonrc` trong project:

```json
{
    "upload": {
        "port": "/dev/ttyUSB0",
        "baud": 115200
    },
    "serial": {
        "port": "/dev/ttyUSB0",
        "baud": 115200
    },
    "ignore": {
        "extensions": [".md", ".git", ".vscode"],
        "directories": ["docs", "examples", ".git"]
    }
}
```

### 6. OTA Updates (Over-The-Air)

#### Cấu hình OTA server

```python
# ota_server.py
import network
import socket
import os

def start_ota_server():
    # Kết nối WiFi
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)
    wlan.connect('YOUR_WIFI', 'PASSWORD')

    # Tạo HTTP server cho OTA
    addr = socket.getaddrinfo('0.0.0.0', 8080)[0][-1]
    s = socket.socket()
    s.bind(addr)
    s.listen(1)

    print(f'OTA Server listening on {wlan.ifconfig()[0]}:8080')

    while True:
        cl, addr = s.accept()
        # Xử lý upload file...
```

#### Upload qua WiFi

```python
# Trên BeE Board
from BeeBrain import bee
import network

# Kết nối WiFi
bee.connect_wifi('SSID', 'PASSWORD')

# Khởi động OTA server
start_ota_server()
```

### 7. Debug và monitoring

#### Serial monitor

```bash
# Theo dõi output real-time
rshell --port /dev/ttyUSB0 repl
```

#### Remote debugging qua WiFi

```python
# Trên BeE Board - enable WebREPL
import webrepl_setup
# Làm theo hướng dẫn setup

# Trên máy tính
# Truy cập http://micropython.org/webrepl/
# Kết nối đến IP của BeE Board
```

### 8. Troubleshooting

#### Lỗi thường gặp

**Lỗi: "Failed to connect"**

```bash
# Kiểm tra port
ls /dev/tty*
# Thử port khác: /dev/ttyUSB1, /dev/ttyACM0

# Reset board bằng cách nhấn nút RESET
# Hoặc hold BOOT + nhấn RESET để vào download mode
```

**Lỗi: "Out of memory"**

```python
# Kiểm tra RAM
import gc
gc.collect()
print(gc.mem_free())

# Tối ưu code, giải phóng biến không dùng
del large_variable
gc.collect()
```

**Lỗi: "Module not found"**

```python
# Kiểm tra modules đã upload
import os
os.listdir('/')

# Upload lại modules
# ampy --port /dev/ttyUSB0 put deploy/ /
```

### 9. Production deployment

#### Tạo firmware release

```bash
# Build firmware với modules tích hợp
make BOARD=ESP32_GENERIC_S3_SPIRAM

# Tạo file release
cp build-ESP32_GENERIC_S3_SPIRAM/firmware.bin BeE-Board-v1.0.0.bin

# Tạo checksum
sha256sum BeE-Board-v1.0.0.bin > BeE-Board-v1.0.0.bin.sha256
```

#### Mass production script

```bash
#!/bin/bash
# flash_production.sh

FIRMWARE="BeE-Board-v1.0.0.bin"
PORT="/dev/ttyUSB0"

echo "Flashing BeE Board..."
esptool.py --chip esp32s3 --port $PORT erase_flash
esptool.py --chip esp32s3 --port $PORT --baud 460800 write_flash -z 0x0 $FIRMWARE

echo "Verifying..."
esptool.py --chip esp32s3 --port $PORT verify_flash 0x0 $FIRMWARE

echo "Done!"
```

---

## Tài liệu tham khảo

-   **[Tài liệu API đầy đủ](docs/README.md)**: Hướng dẫn chi tiết từng module
-   **[BeE IDE](https://beeblock.vn/studio/bee-ide)**: IDE online hỗ trợ Blockly và Python
-   **[Website chính thức](https://beeblock.vn)**: Thông tin sản phẩm và hỗ trợ
-   **[Facebook Community](https://facebook.com/beeblock.vn)**: Thảo luận và hỗ trợ từ cộng đồng

## Giấy phép

Dự án này được phát hành dưới giấy phép Private License – All Rights Reserved. Không được phép sao chép, chia sẻ hoặc sử dụng bất kỳ phần nào của dự án này mà không có sự cho phép bằng văn bản của BeE STEM Solutions.

---

**Được phát triển với ❤️ bởi BeE STEM Solutions Team**

**Liên kết hữu ích:**

-   [Website chính thức](https://beeblock.vn)
-   [IDE Online](https://beeblock.vn/studio/bee-ide)
-   [Facebook](https://facebook.com/beeblock.vn)
-   [Email hỗ trợ](mailto:<EMAIL>)
